//============================================================================
// 【uart.c - 占位文件】
// 用途: 避免编译错误，串口通信功能已移除
// 创建日期: 2025-01-31
// 说明: 仅保留寻迹功能，此模块为占位文件
//============================================================================

#include "stm32f10x.h"

//============================================================================
// 【占位函数实现】
//============================================================================

// 串口初始化占位函数
void UART_Init(void)
{
    // 占位函数 - 无实际功能
    // 串口功能已移除，仅保留寻迹功能
}

// 串口发送字节占位函数
void UART_SendByte(uint8_t data)
{
    // 占位函数 - 无实际功能
    (void)data; // 避免编译警告
}

// 串口接收字节占位函数
uint8_t UART_ReceiveByte(void)
{
    // 占位函数 - 无实际功能
    return 0;
}

// 串口发送字符串占位函数
void UART_SendString(char* str)
{
    // 占位函数 - 无实际功能
    (void)str; // 避免编译警告
}

// 串口中断处理占位函数
void UART_IRQHandler(void)
{
    // 占位函数 - 无实际功能
}

//============================================================================
// 【兼容性函数】
//============================================================================

// 为了兼容可能的调用，提供空的实现
void uart_init(void)
{
    // 兼容性函数
}

void uart_send(uint8_t data)
{
    // 兼容性函数
    (void)data;
}

uint8_t uart_receive(void)
{
    // 兼容性函数
    return 0;
}
