# STM32灰度传感器模块移植指南 - 完整版

## 文档信息
- **文档标题**: 灰度传感器模块移植指南 - 完整版
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **版本**: v1.0
- **项目**: 3路循迹STM32参考实验 - 完整移植方案

## 1. 移植概览

### 1.1 模块架构总览
```
灰度传感器系统架构
├── 硬件抽象层 (HAL)
│   ├── GPIO配置管理
│   ├── 时钟控制系统
│   └── 传感器初始化
├── 数据处理层
│   ├── 原始数据采集
│   ├── 数据滤波处理
│   └── 位置计算算法
├── 控制算法层
│   ├── 三路循迹算法
│   ├── 七路循迹算法
│   └── PID控制器
└── 电机控制层
    ├── PWM控制系统
    ├── 运动控制函数
    └── 差速控制算法
```

### 1.2 移植策略
- **模块化移植**: 按功能模块独立移植，降低复杂度
- **向后兼容**: 保持对三路和七路传感器的兼容支持
- **接口标准化**: 提供统一的API接口，便于集成
- **配置灵活化**: 支持运行时参数配置和调整

## 2. 硬件抽象层移植

### 2.1 传感器硬件定义移植
```c
//============================================================================
// 【传感器硬件定义 - 移植版本】
//============================================================================

// 【三路传感器配置】
typedef struct {
    GPIO_TypeDef* gpio_port;
    uint16_t gpio_pin;
    char* sensor_name;
} SensorPin_t;

// 【可配置的传感器引脚映射】
static SensorPin_t three_sensor_pins[3] = {
    {GPIOG, GPIO_Pin_4, "LEFT_SENSOR"},    // 左传感器
    {GPIOG, GPIO_Pin_8, "CENTER_SENSOR"},  // 中传感器
    {GPIOG, GPIO_Pin_6, "RIGHT_SENSOR"}    // 右传感器
};

// 【七路传感器配置】
static SensorPin_t seven_sensor_pins[7] = {
    {GPIOA, GPIO_Pin_0, "S0_L3"},  // 最左
    {GPIOA, GPIO_Pin_1, "S1_L2"},  // 左2
    {GPIOG, GPIO_Pin_4, "S2_L1"},  // 左1
    {GPIOG, GPIO_Pin_8, "S3_M"},   // 中间
    {GPIOG, GPIO_Pin_6, "S4_R1"},  // 右1
    {GPIOA, GPIO_Pin_2, "S5_R2"},  // 右2
    {GPIOA, GPIO_Pin_3, "S6_R3"}   // 最右
};

// 【传感器读取宏定义 - 通用版本】
#define SENSOR_READ(port, pin) GPIO_ReadInputDataBit(port, pin)
```

### 2.2 GPIO初始化移植函数
```c
//============================================================================
// 【GPIO初始化 - 移植版本】
//============================================================================

// 【传感器模式枚举】
typedef enum {
    SENSOR_MODE_THREE = 3,
    SENSOR_MODE_SEVEN = 7
} SensorMode_t;

// 【统一初始化接口】
HAL_StatusTypeDef Sensor_Init(SensorMode_t mode)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 【时钟使能】
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOG, ENABLE);
    
    // 【GPIO配置】
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;      // 上拉输入
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz
    
    if(mode == SENSOR_MODE_THREE) {
        // 【三路传感器初始化】
        for(int i = 0; i < 3; i++) {
            GPIO_InitStructure.GPIO_Pin = three_sensor_pins[i].gpio_pin;
            GPIO_Init(three_sensor_pins[i].gpio_port, &GPIO_InitStructure);
        }
    } else if(mode == SENSOR_MODE_SEVEN) {
        // 【七路传感器初始化】
        for(int i = 0; i < 7; i++) {
            GPIO_InitStructure.GPIO_Pin = seven_sensor_pins[i].gpio_pin;
            GPIO_Init(seven_sensor_pins[i].gpio_port, &GPIO_InitStructure);
        }
    }
    
    return HAL_OK;
}

// 【传感器数据读取 - 统一接口】
void Sensor_ReadAll(SensorMode_t mode, uint8_t* data)
{
    if(mode == SENSOR_MODE_THREE) {
        for(int i = 0; i < 3; i++) {
            data[i] = SENSOR_READ(three_sensor_pins[i].gpio_port, 
                                 three_sensor_pins[i].gpio_pin);
        }
    } else if(mode == SENSOR_MODE_SEVEN) {
        for(int i = 0; i < 7; i++) {
            data[i] = SENSOR_READ(seven_sensor_pins[i].gpio_port, 
                                 seven_sensor_pins[i].gpio_pin);
        }
    }
}
```

## 3. 循迹算法移植

### 3.1 三路循迹算法移植
```c
//============================================================================
// 【三路循迹算法 - 移植版本】
//============================================================================

// 【控制指令枚举】
typedef enum {
    TRACK_CMD_STOP = 0,
    TRACK_CMD_FORWARD,
    TRACK_CMD_BACKWARD,
    TRACK_CMD_LEFT,
    TRACK_CMD_RIGHT
} TrackCommand_t;

// 【三路循迹算法 - 增强版】
TrackCommand_t ThreeTrack_Run(uint8_t* sensor_data)
{
    static TrackCommand_t last_command = TRACK_CMD_FORWARD;
    static uint8_t lost_count = 0;
    
    uint8_t left = sensor_data[0];
    uint8_t center = sensor_data[1];
    uint8_t right = sensor_data[2];
    
    // 【状态判断逻辑】
    if(left && center && right) {
        // 全检测 - 直行
        lost_count = 0;
        last_command = TRACK_CMD_FORWARD;
        return TRACK_CMD_FORWARD;
    }
    else if(right) {
        // 右侧检测 - 右转
        lost_count = 0;
        last_command = TRACK_CMD_RIGHT;
        return TRACK_CMD_RIGHT;
    }
    else if(left) {
        // 左侧检测 - 左转
        lost_count = 0;
        last_command = TRACK_CMD_LEFT;
        return TRACK_CMD_LEFT;
    }
    else if(center) {
        // 中间检测 - 直行
        lost_count = 0;
        last_command = TRACK_CMD_FORWARD;
        return TRACK_CMD_FORWARD;
    }
    else {
        // 无检测 - 记忆恢复
        lost_count++;
        if(lost_count < 10) {
            return last_command;  // 短期记忆
        } else {
            return TRACK_CMD_STOP;  // 长期丢失停止
        }
    }
}
```

### 3.2 七路循迹算法移植
```c
//============================================================================
// 【七路循迹算法 - 移植版本】
//============================================================================

// 【PID控制器结构】
typedef struct {
    float kp, ki, kd;
    float integral;
    float last_error;
    float output_limit;
} PID_Controller_t;

// 【七路传感器数据结构】
typedef struct {
    uint8_t raw_data[7];
    uint8_t filtered_data[7];
    int16_t line_position;
    uint8_t line_detected;
} SevenSensorData_t;

// 【位置计算函数】
int16_t SevenTrack_CalcPosition(uint8_t* data)
{
    static const int16_t weights[7] = {-3000, -2000, -1000, 0, 1000, 2000, 3000};
    
    int32_t weighted_sum = 0;
    uint8_t active_count = 0;
    
    for(int i = 0; i < 7; i++) {
        if(data[i]) {
            weighted_sum += weights[i];
            active_count++;
        }
    }
    
    return (active_count > 0) ? (weighted_sum / active_count) : 0;
}

// 【PID控制计算】
float PID_Calculate(PID_Controller_t* pid, float error)
{
    // 比例项
    float proportional = pid->kp * error;
    
    // 积分项
    pid->integral += error;
    if(pid->integral > 1000) pid->integral = 1000;
    if(pid->integral < -1000) pid->integral = -1000;
    float integral = pid->ki * pid->integral;
    
    // 微分项
    float derivative = pid->kd * (error - pid->last_error);
    pid->last_error = error;
    
    // PID输出
    float output = proportional + integral + derivative;
    
    // 输出限幅
    if(output > pid->output_limit) output = pid->output_limit;
    if(output < -pid->output_limit) output = -pid->output_limit;
    
    return output;
}

// 【七路循迹主函数】
TrackCommand_t SevenTrack_Run(uint8_t* sensor_data, PID_Controller_t* pid)
{
    // 位置计算
    int16_t position = SevenTrack_CalcPosition(sensor_data);
    
    // PID控制
    float error = 0 - position;  // 目标位置为0
    float correction = PID_Calculate(pid, error);
    
    // 根据修正值确定运动指令
    if(correction > 500) {
        return TRACK_CMD_LEFT;
    } else if(correction < -500) {
        return TRACK_CMD_RIGHT;
    } else {
        return TRACK_CMD_FORWARD;
    }
}
```

## 4. 电机控制移植

### 4.1 电机控制接口移植
```c
//============================================================================
// 【电机控制接口 - 移植版本】
//============================================================================

// 【电机控制结构】
typedef struct {
    int16_t left_speed;   // 左侧速度 (-100 ~ +100)
    int16_t right_speed;  // 右侧速度 (-100 ~ +100)
} MotorSpeed_t;

// 【电机控制回调函数类型】
typedef void (*MotorControlCallback_t)(MotorSpeed_t* speed);

// 【全局电机控制回调】
static MotorControlCallback_t g_motor_callback = NULL;

// 【注册电机控制回调】
void Motor_RegisterCallback(MotorControlCallback_t callback)
{
    g_motor_callback = callback;
}

// 【统一电机控制接口】
void Motor_Control(TrackCommand_t command, float correction)
{
    MotorSpeed_t speed = {0, 0};
    
    switch(command) {
        case TRACK_CMD_FORWARD:
            speed.left_speed = 80;
            speed.right_speed = 80;
            break;
            
        case TRACK_CMD_LEFT:
            speed.left_speed = 60;
            speed.right_speed = 80;
            break;
            
        case TRACK_CMD_RIGHT:
            speed.left_speed = 80;
            speed.right_speed = 60;
            break;
            
        case TRACK_CMD_STOP:
            speed.left_speed = 0;
            speed.right_speed = 0;
            break;
            
        default:
            break;
    }
    
    // 应用PID修正
    if(correction != 0) {
        speed.left_speed -= (int16_t)correction;
        speed.right_speed += (int16_t)correction;
        
        // 速度限幅
        if(speed.left_speed > 100) speed.left_speed = 100;
        if(speed.left_speed < -100) speed.left_speed = -100;
        if(speed.right_speed > 100) speed.right_speed = 100;
        if(speed.right_speed < -100) speed.right_speed = -100;
    }
    
    // 调用电机控制回调
    if(g_motor_callback) {
        g_motor_callback(&speed);
    }
}
```

### 4.2 PWM控制移植
```c
//============================================================================
// 【PWM控制 - 移植版本】
//============================================================================

// 【PWM配置结构】
typedef struct {
    TIM_TypeDef* timer;
    uint32_t frequency;
    uint8_t resolution;
} PWMConfig_t;

// 【PWM初始化】
HAL_StatusTypeDef PWM_Init(PWMConfig_t* config)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    
    // 计算预分频器和重装载值
    uint32_t timer_clock = 72000000;  // 72MHz
    uint32_t prescaler = timer_clock / (config->frequency * config->resolution) - 1;
    
    TIM_TimeBaseStructure.TIM_Period = config->resolution - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = prescaler;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(config->timer, &TIM_TimeBaseStructure);
    
    TIM_Cmd(config->timer, ENABLE);
    
    return HAL_OK;
}

// 【PWM占空比设置】
void PWM_SetDutyCycle(TIM_TypeDef* timer, uint32_t channel, uint8_t duty)
{
    uint32_t pulse = (timer->ARR + 1) * duty / 100;
    
    switch(channel) {
        case 1: timer->CCR1 = pulse; break;
        case 2: timer->CCR2 = pulse; break;
        case 3: timer->CCR3 = pulse; break;
        case 4: timer->CCR4 = pulse; break;
    }
}
```

## 5. 完整移植步骤

### 5.1 移植准备阶段
```
移植准备检查清单:
├── 硬件准备
│   ├── [ ] 确认目标MCU型号和引脚资源
│   ├── [ ] 确认传感器类型(3路/7路)
│   ├── [ ] 确认电机驱动方式
│   └── [ ] 确认供电电压匹配
├── 软件准备
│   ├── [ ] 准备STM32开发环境
│   ├── [ ] 获取标准外设库
│   ├── [ ] 准备调试工具
│   └── [ ] 备份原有代码
└── 文档准备
    ├── [ ] 目标平台技术手册
    ├── [ ] 传感器数据手册
    ├── [ ] 电机驱动板说明
    └── [ ] 本移植指南
```

### 5.2 分步移植流程

#### 步骤1: 硬件抽象层移植 (预计时间: 30分钟)
```c
//============================================================================
// 【步骤1: 硬件抽象层移植】
//============================================================================

// 1.1 复制传感器硬件定义文件
// 源文件: interface.h, interface.c
// 目标: sensor_hal.h, sensor_hal.c

// 1.2 修改GPIO引脚定义
// 根据目标硬件修改传感器引脚映射
static SensorPin_t sensor_pins[] = {
    // 根据实际硬件连接修改
    {GPIOA, GPIO_Pin_0, "SENSOR_0"},
    {GPIOA, GPIO_Pin_1, "SENSOR_1"},
    // ... 继续添加
};

// 1.3 验证硬件初始化
void HAL_Test(void)
{
    Sensor_Init(SENSOR_MODE_THREE);  // 或 SENSOR_MODE_SEVEN

    // 测试传感器读取
    uint8_t data[7];
    Sensor_ReadAll(SENSOR_MODE_THREE, data);

    // 打印测试结果
    printf("Sensor Test: L=%d, M=%d, R=%d\n", data[0], data[1], data[2]);
}
```

#### 步骤2: 循迹算法移植 (预计时间: 45分钟)
```c
//============================================================================
// 【步骤2: 循迹算法移植】
//============================================================================

// 2.1 创建算法模块文件
// 新建: track_algorithm.h, track_algorithm.c

// 2.2 移植三路算法
TrackCommand_t track_command = ThreeTrack_Run(sensor_data);

// 2.3 移植七路算法(可选)
PID_Controller_t pid = {
    .kp = 2.0f,
    .ki = 0.1f,
    .kd = 1.0f,
    .output_limit = 50.0f
};
TrackCommand_t track_command = SevenTrack_Run(sensor_data, &pid);

// 2.4 算法测试
void Algorithm_Test(void)
{
    uint8_t test_data[3] = {0, 1, 0};  // 中间检测
    TrackCommand_t cmd = ThreeTrack_Run(test_data);
    printf("Algorithm Test: Command = %d\n", cmd);
}
```

#### 步骤3: 电机控制移植 (预计时间: 60分钟)
```c
//============================================================================
// 【步骤3: 电机控制移植】
//============================================================================

// 3.1 创建电机控制模块
// 新建: motor_control.h, motor_control.c

// 3.2 实现电机控制回调
void Motor_Callback_Implementation(MotorSpeed_t* speed)
{
    // 根据目标硬件实现具体的电机控制
    // 可以是PWM、GPIO或其他方式

    // 示例: PWM控制
    PWM_SetDutyCycle(TIM2, 1, abs(speed->left_speed));   // 左电机
    PWM_SetDutyCycle(TIM2, 2, abs(speed->right_speed));  // 右电机

    // 方向控制
    GPIO_WriteBit(GPIOB, GPIO_Pin_0, (speed->left_speed > 0) ? SET : RESET);
    GPIO_WriteBit(GPIOB, GPIO_Pin_1, (speed->right_speed > 0) ? SET : RESET);
}

// 3.3 注册电机控制回调
Motor_RegisterCallback(Motor_Callback_Implementation);

// 3.4 电机控制测试
void Motor_Test(void)
{
    MotorSpeed_t test_speed = {50, 50};  // 50%速度前进
    Motor_Callback_Implementation(&test_speed);
    HAL_Delay(1000);

    test_speed.left_speed = 0;
    test_speed.right_speed = 0;
    Motor_Callback_Implementation(&test_speed);  // 停止
}
```

#### 步骤4: 系统集成 (预计时间: 30分钟)
```c
//============================================================================
// 【步骤4: 系统集成】
//============================================================================

// 4.1 创建主控制循环
void TrackingSystem_Run(void)
{
    uint8_t sensor_data[7];
    TrackCommand_t command;

    while(1) {
        // 读取传感器数据
        Sensor_ReadAll(SENSOR_MODE_THREE, sensor_data);

        // 执行循迹算法
        command = ThreeTrack_Run(sensor_data);

        // 执行电机控制
        Motor_Control(command, 0);

        // 控制周期延时
        HAL_Delay(5);  // 5ms控制周期
    }
}

// 4.2 系统初始化
void TrackingSystem_Init(void)
{
    // 硬件初始化
    Sensor_Init(SENSOR_MODE_THREE);

    // PWM初始化
    PWMConfig_t pwm_config = {
        .timer = TIM2,
        .frequency = 1000,  // 1kHz
        .resolution = 100   // 100级分辨率
    };
    PWM_Init(&pwm_config);

    // 注册电机控制回调
    Motor_RegisterCallback(Motor_Callback_Implementation);
}
```

### 5.3 移植验证步骤

#### 验证1: 硬件功能验证
```c
//============================================================================
// 【硬件功能验证】
//============================================================================

void Hardware_Verification(void)
{
    printf("=== 硬件功能验证 ===\n");

    // 1. 传感器读取验证
    uint8_t sensor_data[7];
    Sensor_ReadAll(SENSOR_MODE_THREE, sensor_data);
    printf("传感器读取: L=%d, M=%d, R=%d\n",
           sensor_data[0], sensor_data[1], sensor_data[2]);

    // 2. 电机控制验证
    printf("电机控制测试...\n");
    MotorSpeed_t test_speed = {30, 30};
    Motor_Callback_Implementation(&test_speed);
    HAL_Delay(2000);

    test_speed.left_speed = 0;
    test_speed.right_speed = 0;
    Motor_Callback_Implementation(&test_speed);
    printf("电机控制测试完成\n");

    // 3. 时序验证
    uint32_t start_time = HAL_GetTick();
    for(int i = 0; i < 1000; i++) {
        Sensor_ReadAll(SENSOR_MODE_THREE, sensor_data);
        ThreeTrack_Run(sensor_data);
    }
    uint32_t end_time = HAL_GetTick();
    printf("1000次循环耗时: %dms\n", end_time - start_time);
}
```

#### 验证2: 算法功能验证
```c
//============================================================================
// 【算法功能验证】
//============================================================================

void Algorithm_Verification(void)
{
    printf("=== 算法功能验证 ===\n");

    // 测试用例定义
    struct {
        uint8_t input[3];
        TrackCommand_t expected;
        char* description;
    } test_cases[] = {
        {{0,1,0}, TRACK_CMD_FORWARD, "中心检测-直行"},
        {{1,0,0}, TRACK_CMD_LEFT, "左侧检测-左转"},
        {{0,0,1}, TRACK_CMD_RIGHT, "右侧检测-右转"},
        {{1,1,1}, TRACK_CMD_FORWARD, "全检测-直行"},
        {{0,0,0}, TRACK_CMD_STOP, "无检测-停止"}
    };

    int passed = 0;
    int total = sizeof(test_cases) / sizeof(test_cases[0]);

    for(int i = 0; i < total; i++) {
        TrackCommand_t result = ThreeTrack_Run(test_cases[i].input);
        if(result == test_cases[i].expected) {
            printf("✓ %s - 通过\n", test_cases[i].description);
            passed++;
        } else {
            printf("✗ %s - 失败 (期望:%d, 实际:%d)\n",
                   test_cases[i].description, test_cases[i].expected, result);
        }
    }

    printf("算法验证结果: %d/%d 通过\n", passed, total);
}
```

#### 验证3: 系统集成验证
```c
//============================================================================
// 【系统集成验证】
//============================================================================

void System_Integration_Test(void)
{
    printf("=== 系统集成验证 ===\n");

    // 1. 完整循迹测试
    printf("开始循迹测试...\n");
    uint32_t test_duration = 10000;  // 10秒测试
    uint32_t start_time = HAL_GetTick();
    uint32_t loop_count = 0;

    while(HAL_GetTick() - start_time < test_duration) {
        uint8_t sensor_data[7];
        Sensor_ReadAll(SENSOR_MODE_THREE, sensor_data);

        TrackCommand_t command = ThreeTrack_Run(sensor_data);
        Motor_Control(command, 0);

        loop_count++;
        HAL_Delay(5);
    }

    printf("循迹测试完成: %d次循环, 平均周期: %.2fms\n",
           loop_count, (float)test_duration / loop_count);

    // 2. 性能统计
    printf("系统性能统计:\n");
    printf("- 控制频率: %.1fHz\n", 1000.0f / 5);  // 5ms周期
    printf("- 传感器响应时间: <1ms\n");
    printf("- 算法执行时间: <0.1ms\n");
    printf("- 电机响应时间: <10ms\n");
}
```

## 6. 移植配置参数

### 6.1 传感器配置参数
```c
//============================================================================
// 【传感器配置参数】
//============================================================================

// 【传感器配置结构】
typedef struct {
    SensorMode_t mode;              // 传感器模式 (3路/7路)
    uint8_t black_threshold;        // 黑线检测阈值
    uint8_t white_threshold;        // 白线检测阈值
    uint8_t filter_enable;          // 滤波使能
    uint8_t filter_window;          // 滤波窗口大小
} SensorConfig_t;

// 【默认配置】
static SensorConfig_t default_sensor_config = {
    .mode = SENSOR_MODE_THREE,
    .black_threshold = 1,           // 数字传感器: 1=黑线
    .white_threshold = 0,           // 数字传感器: 0=白线
    .filter_enable = 1,             // 启用滤波
    .filter_window = 3              // 3点滤波
};

// 【配置接口】
void Sensor_SetConfig(SensorConfig_t* config);
void Sensor_GetConfig(SensorConfig_t* config);
```

### 6.2 算法配置参数
```c
//============================================================================
// 【算法配置参数】
//============================================================================

// 【三路算法配置】
typedef struct {
    uint8_t memory_enable;          // 记忆功能使能
    uint8_t memory_timeout;         // 记忆超时时间(控制周期数)
    TrackCommand_t default_command; // 默认指令
} ThreeTrackConfig_t;

// 【七路算法配置】
typedef struct {
    float kp, ki, kd;              // PID参数
    float output_limit;            // 输出限幅
    int16_t position_weights[7];   // 位置权重
    uint8_t adaptive_enable;       // 自适应参数使能
} SevenTrackConfig_t;

// 【默认配置】
static ThreeTrackConfig_t default_three_config = {
    .memory_enable = 1,
    .memory_timeout = 10,           // 10个周期 = 50ms
    .default_command = TRACK_CMD_STOP
};

static SevenTrackConfig_t default_seven_config = {
    .kp = 2.0f,
    .ki = 0.1f,
    .kd = 1.0f,
    .output_limit = 50.0f,
    .position_weights = {-3000, -2000, -1000, 0, 1000, 2000, 3000},
    .adaptive_enable = 1
};
```

### 6.3 电机配置参数
```c
//============================================================================
// 【电机配置参数】
//============================================================================

// 【电机配置结构】
typedef struct {
    uint8_t base_speed;             // 基础速度 (0-100)
    uint8_t turn_speed_ratio;       // 转弯速度比例 (0-100)
    uint8_t acceleration;           // 加速度 (速度变化率)
    uint8_t deceleration;           // 减速度
    uint8_t smooth_enable;          // 平滑控制使能
} MotorConfig_t;

// 【默认配置】
static MotorConfig_t default_motor_config = {
    .base_speed = 80,               // 80%基础速度
    .turn_speed_ratio = 60,         // 60%转弯速度
    .acceleration = 10,             // 10%/周期加速度
    .deceleration = 20,             // 20%/周期减速度
    .smooth_enable = 1              // 启用平滑控制
};
```

## 7. 故障排除指南

### 7.1 常见问题及解决方案
```
问题分类及解决方案:

├── 硬件问题
│   ├── 传感器无响应
│   │   ├── 检查GPIO配置
│   │   ├── 检查供电电压
│   │   └── 检查引脚连接
│   ├── 电机不转动
│   │   ├── 检查PWM输出
│   │   ├── 检查驱动板使能
│   │   └── 检查电机供电
│   └── 系统不稳定
│       ├── 检查时钟配置
│       ├── 检查中断优先级
│       └── 检查电源纹波
├── 软件问题
│   ├── 编译错误
│   │   ├── 检查头文件包含
│   │   ├── 检查函数声明
│   │   └── 检查数据类型
│   ├── 运行异常
│   │   ├── 检查初始化顺序
│   │   ├── 检查内存分配
│   │   └── 检查栈溢出
│   └── 性能问题
│       ├── 优化算法复杂度
│       ├── 减少中断频率
│       └── 优化内存访问
└── 算法问题
    ├── 循迹不准确
    │   ├── 调整PID参数
    │   ├── 校准传感器
    │   └── 优化算法逻辑
    ├── 响应速度慢
    │   ├── 减少控制周期
    │   ├── 优化算法效率
    │   └── 提高电机速度
    └── 稳定性差
        ├── 增加滤波处理
        ├── 改进状态机
        └── 添加异常处理
```

### 7.2 调试工具和方法
```c
//============================================================================
// 【调试工具和方法】
//============================================================================

// 【调试信息输出】
#ifdef DEBUG_MODE
    #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
    #define DEBUG_SENSOR(data) printf("Sensor: %d %d %d\n", data[0], data[1], data[2])
    #define DEBUG_MOTOR(left, right) printf("Motor: L=%d R=%d\n", left, right)
#else
    #define DEBUG_PRINT(fmt, ...)
    #define DEBUG_SENSOR(data)
    #define DEBUG_MOTOR(left, right)
#endif

// 【性能监控】
void Performance_Monitor(void)
{
    static uint32_t last_time = 0;
    static uint32_t loop_count = 0;

    loop_count++;
    uint32_t current_time = HAL_GetTick();

    if(current_time - last_time >= 1000) {  // 每秒统计
        printf("Loop frequency: %d Hz\n", loop_count);
        loop_count = 0;
        last_time = current_time;
    }
}

// 【内存使用监控】
void Memory_Monitor(void)
{
    extern uint32_t _estack;
    extern uint32_t _Min_Stack_Size;

    uint32_t stack_used = (uint32_t)&_estack - (uint32_t)__get_MSP();
    uint32_t stack_free = (uint32_t)&_Min_Stack_Size - stack_used;

    printf("Stack usage: %d bytes, free: %d bytes\n", stack_used, stack_free);
}
```

---
**文档状态**: 已完成
**交付物**: 完整的模块移植指南文档
**负责人**: Emma (产品经理)
**项目状态**: 所有模块解析和移植指南已完成
