#include "interface.h"

void delay_init(void)
{
   SysTick->CTRL&=0xfffffffb;//控制寄存器中选择外部时钟即系统时钟的八分之一（HCLK/8）72M/8=9M。
}

//1us 延时函数
void Delay_us(u32 Nus)   
{   
SysTick->LOAD=Nus*9;          //时间加载    72M分频     
SysTick->CTRL|=0x01;             //开始倒数      
while(!(SysTick->CTRL&(1<<16))); //等待时间到达   
SysTick->CTRL=0X00000000;        //关闭计数器   
SysTick->VAL=0X00000000;         //清空计数器        
} 

void Delayms(u32 Nms)
{
	while(Nms--)
	{
		Delay_us(1000);
	}
}

//使能所有GPIO时钟
void GPIOCLKInit(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOF , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOG , ENABLE);
}

void UserLEDInit(void)
{
  GPIO_InitTypeDef  GPIO_InitStructure;
	
	GPIO_InitStructure.GPIO_Pin = LED_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;//
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(LED_GPIO , &GPIO_InitStructure);
	
	LED_SET;	
}

//============================================================================
// 【传感器初始化函数】
//============================================================================

//红外光电对管初始化 - 原三路传感器
void RedRayInit(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;

	GPIO_InitStructure.GPIO_Pin = SEARCH_M_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_M_GPIO , &GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin = SEARCH_R_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_R_GPIO , &GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin = SEARCH_L_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_L_GPIO , &GPIO_InitStructure);
}

//七路传感器初始化函数
void SevenRayInit(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;

	// 配置GPIO为上拉输入模式
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;

	// 【S0 - 最左传感器 PA0】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S0_PIN;
	GPIO_Init(SEARCH_S0_GPIO, &GPIO_InitStructure);

	// 【S1 - 左2传感器 PA1】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S1_PIN;
	GPIO_Init(SEARCH_S1_GPIO, &GPIO_InitStructure);

	// 【S2 - 左1传感器 PG4 (原左传感器)】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S2_PIN;
	GPIO_Init(SEARCH_S2_GPIO, &GPIO_InitStructure);

	// 【S3 - 中间传感器 PG8 (原中传感器)】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S3_PIN;
	GPIO_Init(SEARCH_S3_GPIO, &GPIO_InitStructure);

	// 【S4 - 右1传感器 PG6 (原右传感器)】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S4_PIN;
	GPIO_Init(SEARCH_S4_GPIO, &GPIO_InitStructure);

	// 【S5 - 右2传感器 PA2】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S5_PIN;
	GPIO_Init(SEARCH_S5_GPIO, &GPIO_InitStructure);

	// 【S6 - 最右传感器 PA3】
	GPIO_InitStructure.GPIO_Pin = SEARCH_S6_PIN;
	GPIO_Init(SEARCH_S6_GPIO, &GPIO_InitStructure);
}


/**-------------------------------------------------------
  * @函数名 NVIC_TIM5Configuration
  * @功能   配置TIM5中断优先级分组和优先级
  * @参数   无
  * @返回值 无
***------------------------------------------------------*/
static void NVIC_TIM2Configuration(void)
{ 
    NVIC_InitTypeDef NVIC_InitStructure;

    /* Set the Vector Table base address at 0x08000000 */
    //NVIC_SetVectorTable(NVIC_VectTab_FLASH, 0x0000);

    /* Enable the TIM5 gloabal Interrupt */
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;

    NVIC_Init(&NVIC_InitStructure);
}


void TIM2_Init(void)
{
    TIM_TimeBaseInitTypeDef  TIM_TimeBaseStructure;

    /* TIM2 clock enable */
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    /* Time base configuration */
    //定时器自动重装载的计数值，由于计数是从0开始的，所以为100us
    TIM_TimeBaseStructure.TIM_Period = (100 - 1);//10kHz
    // 定时器预分频系数，当等于0时，表示不分频，需要+1
    TIM_TimeBaseStructure.TIM_Prescaler = (72 - 1);//1MHz
    // 高级应用本参数不涉及，定义在定时时钟(CK_INT)频率与数字滤波器(ETR,TIx)
    // 使用的采样频率之间的分频比例
    TIM_TimeBaseStructure.TIM_ClockDivision = 0;
    //向上计数
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    //初始化定时器5
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    /* Clear TIM5 update pending flag[清除TIM5更新中断标志] */
    TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

    /* TIM IT enable */ //开启中断
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);

    /* TIM5 enable counter */
    TIM_Cmd(TIM2, ENABLE);  //定时器使能，开始计数

    /* 中断参数配置 */
    NVIC_TIM2Configuration();
}

void LEDToggle(uint16_t Led)
{
    /* 指定管脚输出取反 1变0，实现对应的LED指示灯状态取反的目的 */
	LED_GPIO->ODR ^= Led;
	//如要高效率，可以直接调用 LEDnOBB = !LEDnOBB;
}

//============================================================================
// 【七路寻迹系统核心函数实现】
//============================================================================

// 全局变量定义
SevenLineSensor_t g_seven_sensor = {0};     // 七路传感器数据
PID_Controller_t g_line_pid = {0};          // 循迹PID控制器
uint8_t g_seven_mode_enabled = 0;           // 七路模式使能标志

// 读取七路传感器原始数据
void SevenSensor_ReadRaw(uint8_t data[7])
{
    data[0] = SEARCH_S0_IO;  // 最左传感器
    data[1] = SEARCH_S1_IO;  // 左2传感器
    data[2] = SEARCH_S2_IO;  // 左1传感器
    data[3] = SEARCH_S3_IO;  // 中间传感器
    data[4] = SEARCH_S4_IO;  // 右1传感器
    data[5] = SEARCH_S5_IO;  // 右2传感器
    data[6] = SEARCH_S6_IO;  // 最右传感器
}

// 传感器数据滤波 (简单移动平均)
void SevenSensor_Filter(uint8_t raw[7], uint8_t filtered[7])
{
    static uint8_t history[7][3] = {0};  // 3次历史数据
    static uint8_t index = 0;

    // 更新历史数据
    for(int i = 0; i < 7; i++) {
        history[i][index] = raw[i];

        // 计算3次平均值
        uint8_t sum = history[i][0] + history[i][1] + history[i][2];
        filtered[i] = (sum >= 2) ? 1 : 0;  // 多数表决
    }

    index = (index + 1) % 3;
}

// 计算黑线位置 (加权平均算法)
int16_t SevenSensor_CalcPosition(uint8_t data[7])
{
    // 位置权重数组 (-3000 到 +3000)
    static const int16_t weights[7] = {-3000, -2000, -1000, 0, 1000, 2000, 3000};

    int32_t weighted_sum = 0;
    uint8_t active_count = 0;

    // 计算加权平均
    for(int i = 0; i < 7; i++) {
        if(data[i] == BLACK_AREA) {
            weighted_sum += weights[i];
            active_count++;
        }
    }

    // 返回位置值
    if(active_count > 0) {
        return (int16_t)(weighted_sum / active_count);
    }

    return 0;  // 未检测到黑线
}

// 判断循迹状态
TrackState_t SevenSensor_GetState(uint8_t data[7])
{
    uint8_t pattern = 0;
    uint8_t active_count = 0;

    // 生成传感器模式
    for(int i = 0; i < 7; i++) {
        if(data[i] == BLACK_AREA) {
            pattern |= (1 << i);
            active_count++;
        }
    }

    // 状态判断
    if(active_count == 0) {
        return TRACK_LOST;          // 无传感器检测到
    } else if(active_count >= 5) {
        return TRACK_INTERSECTION;  // 多传感器检测到
    } else if(pattern & 0x07) {     // 左侧传感器 (S0,S1,S2)
        return TRACK_SHARP_LEFT;
    } else if(pattern & 0x70) {     // 右侧传感器 (S4,S5,S6)
        return TRACK_SHARP_RIGHT;
    } else {
        return TRACK_NORMAL;        // 正常循迹
    }
}

// PID控制器初始化
void SevenTrack_PIDInit(PID_Controller_t* pid)
{
    // PID参数设置 (定点运算 * 1000)
    pid->kp = 2000;         // Kp = 2.0
    pid->ki = 100;          // Ki = 0.1
    pid->kd = 500;          // Kd = 0.5

    pid->last_error = 0;
    pid->integral = 0;
    pid->output = 0;
    pid->output_limit = 1000;
}

// PID控制计算 (定点运算版本)
int16_t SevenTrack_PIDCalc(PID_Controller_t* pid, int16_t error)
{
    // 比例项
    int32_t proportional = (pid->kp * error) / 1000;

    // 积分项 (带限制)
    pid->integral += error;
    if(pid->integral > 10000) pid->integral = 10000;
    if(pid->integral < -10000) pid->integral = -10000;
    int32_t integral = (pid->ki * pid->integral) / 1000;

    // 微分项
    int32_t derivative = (pid->kd * (error - pid->last_error)) / 1000;
    pid->last_error = error;

    // PID输出
    int32_t output = proportional + integral + derivative;

    // 输出限制
    if(output > pid->output_limit) output = pid->output_limit;
    if(output < -pid->output_limit) output = -pid->output_limit;

    pid->output = (int16_t)output;
    return pid->output;
}

// 电机差速控制
void SevenTrack_MotorControl(int16_t correction)
{
    // 基础速度
    int16_t base_speed = SPEED_DUTY;

    // 计算左右电机速度
    int16_t left_speed = base_speed - correction / 20;   // 左电机速度
    int16_t right_speed = base_speed + correction / 20;  // 右电机速度

    // 速度限制
    if(left_speed > 50) left_speed = 50;
    if(left_speed < -50) left_speed = -50;
    if(right_speed > 50) right_speed = 50;
    if(right_speed < -50) right_speed = -50;

    // 应用到电机
    front_left_speed_duty = left_speed;
    front_right_speed_duty = right_speed;
    behind_left_speed_duty = left_speed;
    behind_right_speed_duty = right_speed;
}

// 七路循迹主控制函数
void SevenTrack_Run(void)
{
    // 读取传感器数据
    SevenSensor_ReadRaw(g_seven_sensor.sensor_raw);

    // 数据滤波
    SevenSensor_Filter(g_seven_sensor.sensor_raw, g_seven_sensor.sensor_filtered);

    // 计算黑线位置
    g_seven_sensor.line_position = SevenSensor_CalcPosition(g_seven_sensor.sensor_filtered);

    // 判断循迹状态
    g_seven_sensor.track_state = SevenSensor_GetState(g_seven_sensor.sensor_filtered);

    // 计算位置误差 (目标位置为0)
    int16_t position_error = 0 - g_seven_sensor.line_position;

    // PID控制计算
    int16_t correction = SevenTrack_PIDCalc(&g_line_pid, position_error);

    // 根据状态调整控制策略
    switch(g_seven_sensor.track_state) {
        case TRACK_NORMAL:
            // 正常循迹 - 使用PID控制
            SevenTrack_MotorControl(correction);
            break;

        case TRACK_SHARP_LEFT:
            // 急左转 - 增强左转力度
            SevenTrack_MotorControl(correction * 2);
            break;

        case TRACK_SHARP_RIGHT:
            // 急右转 - 增强右转力度
            SevenTrack_MotorControl(correction * 2);
            break;

        case TRACK_LOST:
            // 失去黑线 - 保持上次动作
            break;

        case TRACK_INTERSECTION:
            // 交叉路口 - 直行
            SevenTrack_MotorControl(0);
            break;

        default:
            break;
    }
}
