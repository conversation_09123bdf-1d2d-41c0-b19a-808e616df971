# STM32CubeMX灰度传感器引脚配置详细指南

## 文档信息
- **文档标题**: STM32CubeMX灰度传感器引脚配置指南
- **创建日期**: 2025-01-31
- **负责人**: Bob (架构师)
- **版本**: v1.0
- **项目**: STM32七路循迹系统CubeMX配置

## 1. 配置总览

### 1.1 引脚分配表
```
【七路灰度传感器引脚分配】
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│ 传感器编号  │ 功能描述    │ STM32引脚   │ GPIO端口    │ CubeMX标签  │
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ D1 (S0)     │ 最左传感器  │ PA0         │ GPIOA       │ SENSOR_D1   │
│ D2 (S1)     │ 左2传感器   │ PA1         │ GPIOA       │ SENSOR_D2   │
│ D3 (S2)     │ 左1传感器   │ PG4         │ GPIOG       │ SENSOR_D3   │
│ D4 (S3)     │ 中间传感器  │ PG8         │ GPIOG       │ SENSOR_D4   │
│ D5 (S4)     │ 右1传感器   │ PG6         │ GPIOG       │ SENSOR_D5   │
│ D6 (S5)     │ 右2传感器   │ PA2         │ GPIOA       │ SENSOR_D6   │
│ D7 (S6)     │ 最右传感器  │ PA3         │ GPIOA       │ SENSOR_D7   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘

【传感器布局】
D1    D2    D3    D4    D5    D6    D7
L3    L2    L1    M     R1    R2    R3
PA0   PA1   PG4   PG8   PG6   PA2   PA3
```

### 1.2 配置参数统一标准
```
【所有传感器统一配置】
- GPIO模式: Input mode
- GPIO Pull-up/Pull-down: Pull-up
- Maximum output speed: High
- User Label: SENSOR_D1 ~ SENSOR_D7
```

## 2. CubeMX配置步骤详解

### 2.1 第一步: 新建项目
```
【项目创建】
1. 打开STM32CubeMX
2. 选择 "New Project"
3. 选择目标芯片: STM32F103ZE (或对应型号)
4. 点击 "Start Project"
```

### 2.2 第二步: 引脚配置
```
【引脚配置操作步骤】

步骤1: 配置PA0 (D1传感器)
├── 在Pinout视图中找到PA0引脚
├── 右键点击PA0
├── 选择 "GPIO_Input"
├── 在右侧Configuration中设置:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D1
└── 点击应用

步骤2: 配置PA1 (D2传感器)
├── 找到PA1引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D2
└── 应用配置

步骤3: 配置PA2 (D6传感器)
├── 找到PA2引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D6
└── 应用配置

步骤4: 配置PA3 (D7传感器)
├── 找到PA3引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D7
└── 应用配置

步骤5: 配置PG4 (D3传感器)
├── 找到PG4引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D3
└── 应用配置

步骤6: 配置PG6 (D5传感器)
├── 找到PG6引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D5
└── 应用配置

步骤7: 配置PG8 (D4传感器)
├── 找到PG8引脚，右键选择 "GPIO_Input"
├── 配置参数:
│   ├── GPIO mode: Input mode
│   ├── GPIO Pull-up/Pull-down: Pull-up
│   ├── Maximum output speed: High
│   └── User Label: SENSOR_D4
└── 应用配置
```

### 2.3 第三步: GPIO详细配置
```
【GPIO Configuration详细设置】

在左侧Categories中选择 "GPIO":

┌─────────────────────────────────────────────────────────┐
│ GPIO Configuration                                      │
├─────────────────────────────────────────────────────────┤
│ Pin Name    │ Signal    │ GPIO mode    │ Pull-up/down   │
├─────────────┼───────────┼──────────────┼────────────────┤
│ PA0         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PA1         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PA2         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PA3         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PG4         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PG6         │ GPIO_Input│ Input mode   │ Pull-up        │
│ PG8         │ GPIO_Input│ Input mode   │ Pull-up        │
└─────────────┴───────────┴──────────────┴────────────────┘

【每个引脚的详细配置】
对于每个传感器引脚，确保以下设置:
├── GPIO output level: N/A (输入模式不适用)
├── GPIO mode: Input mode
├── GPIO Pull-up/Pull-down: Pull-up
├── Maximum output speed: High
├── Fast Mode: Disabled
└── User Label: SENSOR_D1~D7 (对应标签)
```

## 3. 时钟配置

### 3.1 RCC时钟配置
```
【时钟配置步骤】

步骤1: 进入Clock Configuration
├── 点击顶部 "Clock Configuration" 标签
├── 确认系统时钟配置:
│   ├── System Clock Mux: PLLCLK
│   ├── PLL Source Mux: HSE
│   ├── PLL MUL: x9 (8MHz * 9 = 72MHz)
│   └── HCLK: 72MHz
└── APB2 Prescaler: /1 (确保GPIO时钟为72MHz)

步骤2: 验证GPIO时钟
├── 确认APB2 peripheral clocks包含:
│   ├── GPIOA Clock: Enabled
│   ├── GPIOG Clock: Enabled
│   └── 频率: 72MHz
└── 这些时钟在代码生成时自动使能
```

### 3.2 自动生成的时钟代码
```c
//============================================================================
// 【CubeMX自动生成的时钟配置代码】
//============================================================================

void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    // 【HSE配置】
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;  // 8MHz * 9 = 72MHz
    HAL_RCC_OscConfig(&RCC_OscInitStruct);

    // 【系统时钟配置】
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;   // HCLK = 72MHz
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;    // PCLK1 = 36MHz
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;    // PCLK2 = 72MHz (GPIO时钟)
    HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2);
}
```

## 4. 代码生成配置

### 4.1 Project Manager设置
```
【Project Manager配置】

步骤1: 项目设置
├── Project Name: STM32_SevenSensor_Tracking
├── Project Location: 选择合适的目录
├── Toolchain/IDE: 选择开发环境
│   ├── Keil MDK-ARM V5
│   ├── STM32CubeIDE
│   └── IAR EWARM
└── 其他设置保持默认

步骤2: Code Generator设置
├── STM32Cube MCU packages and embedded software packs
│   └── 选择最新版本的HAL库
├── Generated files:
│   ├── Copy only the necessary library files
│   ├── Generate peripheral initialization as a pair of '.c/.h' files per peripheral
│   └── Keep User Code when re-generating
└── HAL Settings:
    ├── Set all free pins as analog (to save power)
    ├── Enable Full Assert
    └── Use HAL library (推荐)
```

### 4.2 生成的GPIO初始化代码
```c
//============================================================================
// 【CubeMX自动生成的GPIO初始化代码】
//============================================================================

static void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOG_CLK_ENABLE();

    /*Configure GPIO pins : SENSOR_D1_Pin SENSOR_D2_Pin SENSOR_D6_Pin SENSOR_D7_Pin */
    GPIO_InitStruct.Pin = SENSOR_D1_Pin|SENSOR_D2_Pin|SENSOR_D6_Pin|SENSOR_D7_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /*Configure GPIO pins : SENSOR_D3_Pin SENSOR_D5_Pin SENSOR_D4_Pin */
    GPIO_InitStruct.Pin = SENSOR_D3_Pin|SENSOR_D5_Pin|SENSOR_D4_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);
}
```

### 4.3 生成的引脚定义
```c
//============================================================================
// 【main.h中自动生成的引脚定义】
//============================================================================

/* Private defines -----------------------------------------------------------*/
#define SENSOR_D1_Pin GPIO_PIN_0
#define SENSOR_D1_GPIO_Port GPIOA
#define SENSOR_D2_Pin GPIO_PIN_1
#define SENSOR_D2_GPIO_Port GPIOA
#define SENSOR_D6_Pin GPIO_PIN_2
#define SENSOR_D6_GPIO_Port GPIOA
#define SENSOR_D7_Pin GPIO_PIN_3
#define SENSOR_D7_GPIO_Port GPIOA
#define SENSOR_D3_Pin GPIO_PIN_4
#define SENSOR_D3_GPIO_Port GPIOG
#define SENSOR_D5_Pin GPIO_PIN_6
#define SENSOR_D5_GPIO_Port GPIOG
#define SENSOR_D4_Pin GPIO_PIN_8
#define SENSOR_D4_GPIO_Port GPIOG
```

## 5. 配置验证

### 5.1 配置检查清单
```
【配置验证清单】
┌─────────────────────────────────────────────────────────┐
│ □ 所有7个传感器引脚已配置为GPIO_Input                   │
│ □ 所有引脚Pull-up/Pull-down设置为Pull-up                │
│ □ 所有引脚Maximum output speed设置为High                │
│ □ 所有引脚都有正确的User Label (SENSOR_D1~D7)          │
│ □ GPIOA和GPIOG时钟已自动使能                            │
│ □ 系统时钟配置为72MHz                                   │
│ □ APB2时钟配置为72MHz (GPIO时钟)                        │
│ □ 代码生成设置正确                                      │
│ □ 项目名称和路径设置合理                                │
│ □ 开发环境选择正确                                      │
└─────────────────────────────────────────────────────────┘
```

### 5.2 常见配置错误
```
【常见错误及解决方案】
┌─────────────────┬─────────────────┬─────────────────┐
│ 错误现象        │ 可能原因        │ 解决方案        │
├─────────────────┼─────────────────┼─────────────────┤
│ 引脚无法选择    │ 引脚被其他功能  │ 检查引脚冲突，  │
│ GPIO_Input      │ 占用            │ 释放冲突功能    │
├─────────────────┼─────────────────┼─────────────────┤
│ 编译时找不到    │ User Label未设置│ 重新设置User    │
│ 引脚定义        │ 或设置错误      │ Label并重新生成 │
├─────────────────┼─────────────────┼─────────────────┤
│ GPIO读取异常    │ Pull-up未设置   │ 确保所有引脚都  │
│                 │                 │ 设置为Pull-up   │
├─────────────────┼─────────────────┼─────────────────┤
│ 时钟配置错误    │ APB2时钟设置    │ 检查Clock       │
│                 │ 不正确          │ Configuration   │
├─────────────────┼─────────────────┼─────────────────┤
│ 代码生成失败    │ 项目路径或名称  │ 检查路径权限和  │
│                 │ 包含特殊字符    │ 名称合法性      │
└─────────────────┴─────────────────┴─────────────────┘
```

## 6. 代码适配与集成

### 6.1 HAL库与标准库对比
```c
//============================================================================
// 【标准库 vs HAL库 API对比】
//============================================================================

// 【标准库方式 (原项目)】
#define SEARCH_S0_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0)
#define SEARCH_S1_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1)
#define SEARCH_S2_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_4)
#define SEARCH_S3_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_8)
#define SEARCH_S4_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_6)
#define SEARCH_S5_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_2)
#define SEARCH_S6_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_3)

// 【HAL库方式 (CubeMX生成)】
#define SEARCH_S0_IO  HAL_GPIO_ReadPin(SENSOR_D1_GPIO_Port, SENSOR_D1_Pin)
#define SEARCH_S1_IO  HAL_GPIO_ReadPin(SENSOR_D2_GPIO_Port, SENSOR_D2_Pin)
#define SEARCH_S2_IO  HAL_GPIO_ReadPin(SENSOR_D3_GPIO_Port, SENSOR_D3_Pin)
#define SEARCH_S3_IO  HAL_GPIO_ReadPin(SENSOR_D4_GPIO_Port, SENSOR_D4_Pin)
#define SEARCH_S4_IO  HAL_GPIO_ReadPin(SENSOR_D5_GPIO_Port, SENSOR_D5_Pin)
#define SEARCH_S5_IO  HAL_GPIO_ReadPin(SENSOR_D6_GPIO_Port, SENSOR_D6_Pin)
#define SEARCH_S6_IO  HAL_GPIO_ReadPin(SENSOR_D7_GPIO_Port, SENSOR_D7_Pin)

// 【兼容性宏定义】
// 在main.h中添加以下定义，保持与原代码兼容:
#define D1  (HAL_GPIO_ReadPin(SENSOR_D1_GPIO_Port, SENSOR_D1_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D2  (HAL_GPIO_ReadPin(SENSOR_D2_GPIO_Port, SENSOR_D2_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D3  (HAL_GPIO_ReadPin(SENSOR_D3_GPIO_Port, SENSOR_D3_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D4  (HAL_GPIO_ReadPin(SENSOR_D4_GPIO_Port, SENSOR_D4_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D5  (HAL_GPIO_ReadPin(SENSOR_D5_GPIO_Port, SENSOR_D5_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D6  (HAL_GPIO_ReadPin(SENSOR_D6_GPIO_Port, SENSOR_D6_Pin) == GPIO_PIN_SET ? 1 : 0)
#define D7  (HAL_GPIO_ReadPin(SENSOR_D7_GPIO_Port, SENSOR_D7_Pin) == GPIO_PIN_SET ? 1 : 0)
```

### 6.2 主函数集成
```c
//============================================================================
// 【main.c主函数集成示例】
//============================================================================

/* USER CODE BEGIN Includes */
#include "main.h"
/* USER CODE END Includes */

/* USER CODE BEGIN PV */
// 【老板定制算法声明】
void xunxian_7(void);
void motor(int16_t left_speed, int16_t right_speed);

// 【电机控制变量】
int16_t front_left_speed_duty = 0;
int16_t behind_left_speed_duty = 0;
int16_t front_right_speed_duty = 0;
int16_t behind_right_speed_duty = 0;
/* USER CODE END PV */

int main(void)
{
    /* USER CODE BEGIN 1 */
    /* USER CODE END 1 */

    /* MCU Configuration--------------------------------------------------------*/
    HAL_Init();                    // HAL库初始化
    SystemClock_Config();          // 系统时钟配置
    MX_GPIO_Init();               // GPIO初始化 (包含传感器)

    /* USER CODE BEGIN 2 */
    // 【用户初始化代码】
    // 电机初始化、定时器初始化等
    /* USER CODE END 2 */

    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        /* USER CODE END WHILE */

        /* USER CODE BEGIN 3 */
        // 【主循环 - 5ms周期控制】
        static uint32_t last_tick = 0;
        if(HAL_GetTick() - last_tick >= 5) {  // 5ms周期
            last_tick = HAL_GetTick();
            xunxian_7();  // 执行老板定制算法
        }
        /* USER CODE END 3 */
    }
}

/* USER CODE BEGIN 4 */
//============================================================================
// 【老板定制算法实现】
//============================================================================

void motor(int16_t left_speed, int16_t right_speed)
{
    // 【电机控制接口】
    front_left_speed_duty = left_speed;
    behind_left_speed_duty = left_speed;
    front_right_speed_duty = right_speed;
    behind_right_speed_duty = right_speed;

    // 【实际PWM控制代码】
    // 根据具体电机驱动方式实现
}

void xunxian_7(void)
{
    // 【老板的完整算法】
    if((D4 == 0)&&(D3 != 0)&&(D5 != 0)) {
        motor(40,40);  // 直行
    }
    else if((D1 == 0)&&(D2 == 0)&&(D3 == 0)) {
        motor(40,40);  // 宽黑线直行
    }
    // ... 其他15种情况的完整逻辑
    else {
        motor(20,20);  // 默认搜索模式
    }
}
/* USER CODE END 4 */
```

### 6.3 定时器PWM配置 (可选)
```
【如需要硬件PWM，可在CubeMX中配置定时器】

步骤1: 配置TIM2用于PWM
├── 在Pinout视图中找到TIM2
├── 设置TIM2_CH1, TIM2_CH2为PWM Generation CHx
├── 配置参数:
│   ├── Prescaler: 71 (72MHz/72 = 1MHz)
│   ├── Counter Period: 999 (1MHz/1000 = 1kHz PWM)
│   ├── Pulse: 0 (初始占空比0%)
│   └── PWM Mode: PWM Mode 1
└── 生成代码后在HAL_TIM_PWM_Start()中启动PWM

步骤2: 电机控制引脚配置
├── 配置电机方向控制引脚为GPIO_Output
├── 配置电机使能引脚为TIM_PWM
└── 在motor()函数中调用HAL_TIM_PWM_SetCompare()
```

## 7. 调试与验证

### 7.1 硬件连接验证
```
【硬件连接检查】
┌─────────────────┬─────────────────┬─────────────────┐
│ 检查项目        │ 检查方法        │ 预期结果        │
├─────────────────┼─────────────────┼─────────────────┤
│ 电源连接        │ 万用表测量3.3V  │ 3.3V±0.1V       │
│ 传感器信号线    │ 示波器观察      │ TTL电平信号     │
│ 上拉电阻        │ 悬空时测电压    │ 接近3.3V        │
│ 接地连接        │ 万用表通断测试  │ 导通良好        │
│ 信号完整性      │ 快速移动传感器  │ 信号稳定变化    │
└─────────────────┴─────────────────┴─────────────────┘
```

### 7.2 软件调试代码
```c
//============================================================================
// 【调试代码示例】
//============================================================================

/* USER CODE BEGIN 0 */
#include <stdio.h>

// 【串口重定向 (可选)】
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}

// 【传感器状态调试函数】
void Debug_PrintSensorStatus(void)
{
    printf("传感器状态: D1:%d D2:%d D3:%d D4:%d D5:%d D6:%d D7:%d\r\n",
           D1, D2, D3, D4, D5, D6, D7);

    // 【原始GPIO状态】
    printf("GPIO原始值: PA0:%d PA1:%d PG4:%d PG8:%d PG6:%d PA2:%d PA3:%d\r\n",
           HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0),
           HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_1),
           HAL_GPIO_ReadPin(GPIOG, GPIO_PIN_4),
           HAL_GPIO_ReadPin(GPIOG, GPIO_PIN_8),
           HAL_GPIO_ReadPin(GPIOG, GPIO_PIN_6),
           HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_2),
           HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_3));
}

// 【GPIO寄存器状态检查】
void Debug_CheckGPIORegisters(void)
{
    printf("GPIOA->IDR: 0x%04X\r\n", GPIOA->IDR);
    printf("GPIOG->IDR: 0x%04X\r\n", GPIOG->IDR);
    printf("RCC->APB2ENR: 0x%08X\r\n", RCC->APB2ENR);
}
/* USER CODE END 0 */

// 【在主循环中调用调试函数】
/* USER CODE BEGIN 3 */
static uint32_t debug_tick = 0;
if(HAL_GetTick() - debug_tick >= 100) {  // 100ms调试输出
    debug_tick = HAL_GetTick();
    Debug_PrintSensorStatus();
}
/* USER CODE END 3 */
```

## 8. 性能优化建议

### 8.1 GPIO读取优化
```c
//============================================================================
// 【高性能GPIO读取】
//============================================================================

// 【批量读取优化】
typedef struct {
    uint8_t d1 : 1;
    uint8_t d2 : 1;
    uint8_t d3 : 1;
    uint8_t d4 : 1;
    uint8_t d5 : 1;
    uint8_t d6 : 1;
    uint8_t d7 : 1;
    uint8_t reserved : 1;
} SensorData_t;

// 【快速读取函数】
SensorData_t ReadAllSensors_Fast(void)
{
    SensorData_t data;
    uint32_t gpioa_idr = GPIOA->IDR;  // 一次读取GPIOA所有引脚
    uint32_t gpiog_idr = GPIOG->IDR;  // 一次读取GPIOG所有引脚

    data.d1 = (gpioa_idr & GPIO_PIN_0) ? 1 : 0;  // PA0
    data.d2 = (gpioa_idr & GPIO_PIN_1) ? 1 : 0;  // PA1
    data.d6 = (gpioa_idr & GPIO_PIN_2) ? 1 : 0;  // PA2
    data.d7 = (gpioa_idr & GPIO_PIN_3) ? 1 : 0;  // PA3
    data.d3 = (gpiog_idr & GPIO_PIN_4) ? 1 : 0;  // PG4
    data.d5 = (gpiog_idr & GPIO_PIN_6) ? 1 : 0;  // PG6
    data.d4 = (gpiog_idr & GPIO_PIN_8) ? 1 : 0;  // PG8

    return data;
}

// 【内联宏定义 (最高性能)】
#define READ_ALL_SENSORS_INLINE() do { \
    uint32_t gpioa_data = GPIOA->IDR; \
    uint32_t gpiog_data = GPIOG->IDR; \
    sensor_d1 = (gpioa_data & GPIO_PIN_0) ? 1 : 0; \
    sensor_d2 = (gpioa_data & GPIO_PIN_1) ? 1 : 0; \
    sensor_d6 = (gpioa_data & GPIO_PIN_2) ? 1 : 0; \
    sensor_d7 = (gpioa_data & GPIO_PIN_3) ? 1 : 0; \
    sensor_d3 = (gpiog_data & GPIO_PIN_4) ? 1 : 0; \
    sensor_d5 = (gpiog_data & GPIO_PIN_6) ? 1 : 0; \
    sensor_d4 = (gpiog_data & GPIO_PIN_8) ? 1 : 0; \
} while(0)
```

### 8.2 编译优化设置
```
【Keil MDK编译优化】
Project -> Options for Target -> C/C++:
├── Optimization: Level 2 (-O2)
├── One ELF Section per Function: 勾选
├── Enum Container always int: 勾选
└── Define: USE_HAL_DRIVER, STM32F103xE

【GCC编译优化】
在Makefile中添加:
CFLAGS += -O2 -ffunction-sections -fdata-sections
LDFLAGS += -Wl,--gc-sections
```

---
**配置完成检查清单**:
- ✅ 7个传感器引脚正确配置为GPIO输入
- ✅ 所有引脚设置为上拉输入模式
- ✅ 时钟配置正确 (72MHz系统时钟)
- ✅ 代码生成设置合理
- ✅ HAL库兼容性宏定义完成
- ✅ 调试代码准备就绪
- ✅ 性能优化方案提供

**技术支持**: 如遇到配置问题，可参考本文档第5.2节常见错误解决方案
