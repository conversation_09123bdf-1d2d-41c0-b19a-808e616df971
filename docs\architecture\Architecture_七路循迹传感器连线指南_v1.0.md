# STM32七路循迹传感器连线指南

## 文档信息
- **文档标题**: STM32七路循迹传感器连线指南
- **创建日期**: 2025-01-31
- **负责人**: Bob (架构师)
- **版本**: v1.0
- **项目**: STM32七路循迹系统硬件连接

## 1. 连线总览

### 1.1 传感器布局图
```
七路传感器物理布局 (从左到右):
┌────┬────┬────┬────┬────┬────┬────┐
│ D1 │ D2 │ D3 │ D4 │ D5 │ D6 │ D7 │
│ S0 │ S1 │ S2 │ S3 │ S4 │ S5 │ S6 │
│ L3 │ L2 │ L1 │ M  │ R1 │ R2 │ R3 │
└────┴────┴────┴────┴────┴────┴────┘

传感器间距: 15mm
总检测宽度: 90mm
检测高度: 2-10mm
```

### 1.2 STM32引脚分配表
| 传感器编号 | 代码标识 | 功能描述 | STM32引脚 | GPIO端口 | 引脚编号 |
|------------|----------|----------|-----------|----------|----------|
| D1 | S0 | 最左传感器 | PA0 | GPIOA | GPIO_Pin_0 |
| D2 | S1 | 左2传感器 | PA1 | GPIOA | GPIO_Pin_1 |
| D3 | S2 | 左1传感器 | PG4 | GPIOG | GPIO_Pin_4 |
| D4 | S3 | 中间传感器 | PG8 | GPIOG | GPIO_Pin_8 |
| D5 | S4 | 右1传感器 | PG6 | GPIOG | GPIO_Pin_6 |
| D6 | S5 | 右2传感器 | PA2 | GPIOA | GPIO_Pin_2 |
| D7 | S6 | 最右传感器 | PA3 | GPIOA | GPIO_Pin_3 |

## 2. 详细连线图

### 2.1 STM32F103ZE引脚连接图
```
STM32F103ZE开发板                    七路循迹传感器模块
┌─────────────────────┐             ┌─────────────────────┐
│                     │             │                     │
│  PA0 ───────────────┼─────────────┤ D1 (最左传感器)     │
│  PA1 ───────────────┼─────────────┤ D2 (左2传感器)      │
│  PA2 ───────────────┼─────────────┤ D6 (右2传感器)      │
│  PA3 ───────────────┼─────────────┤ D7 (最右传感器)     │
│                     │             │                     │
│  PG4 ───────────────┼─────────────┤ D3 (左1传感器)      │
│  PG6 ───────────────┼─────────────┤ D5 (右1传感器)      │
│  PG8 ───────────────┼─────────────┤ D4 (中间传感器)     │
│                     │             │                     │
│  VCC (3.3V) ────────┼─────────────┤ VCC                 │
│  GND ───────────────┼─────────────┤ GND                 │
│                     │             │                     │
└─────────────────────┘             └─────────────────────┘
```

### 2.2 传感器模块接线端子
```
传感器模块接线端子 (从左到右):
┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│ VCC │ GND │ D1  │ D2  │ D3  │ D4  │ D5  │ D6  │ D7  │
└─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘
   │     │     │     │     │     │     │     │     │
   │     │     │     │     │     │     │     │     │
   ▼     ▼     ▼     ▼     ▼     ▼     ▼     ▼     ▼
  3.3V  GND   PA0   PA1   PG4   PG8   PG6   PA2   PA3
```

## 3. 分步连线指南

### 3.1 第一步: 电源连接
```
【电源连接 - 最重要】
┌─────────────────────────────────────────────────────────┐
│ 1. VCC → STM32的3.3V电源输出                            │
│ 2. GND → STM32的GND地线                                 │
│                                                         │
│ ⚠️ 注意事项:                                            │
│ - 必须使用3.3V，不能使用5V (会损坏传感器)               │
│ - 确保GND连接牢固，接触不良会导致误检测                 │
│ - 电源线建议使用较粗的导线 (≥0.5mm²)                   │
└─────────────────────────────────────────────────────────┘
```

### 3.2 第二步: 信号线连接
```
【信号线连接顺序】
连接顺序建议: 从中间向两边连接，便于调试

1. 中间传感器 (最重要):
   D4 → PG8 (中间传感器，循迹核心)

2. 左右1传感器:
   D3 → PG4 (左1传感器)
   D5 → PG6 (右1传感器)

3. 左右2传感器:
   D2 → PA1 (左2传感器)
   D6 → PA2 (右2传感器)

4. 最外侧传感器:
   D1 → PA0 (最左传感器)
   D7 → PA3 (最右传感器)
```

### 3.3 第三步: 连接验证
```
【连接验证步骤】
1. 目视检查: 确认所有连线正确，无短路
2. 万用表测试: 测量VCC为3.3V，GND为0V
3. 信号测试: 用万用表测量信号线电压
   - 传感器悬空时: 应为3.3V (上拉电阻作用)
   - 传感器检测黑线时: 应为0V
4. 软件测试: 运行测试程序验证读取正常
```

## 4. 传感器模块选型

### 4.1 推荐传感器模块
```
【推荐型号】
1. TCRT5000 红外反射式传感器 × 7
   - 检测距离: 2-10mm
   - 工作电压: 3.3V-5V
   - 输出类型: 数字信号 (0/1)
   - 响应时间: <1ms

2. 七路循迹传感器模块 (集成版)
   - 型号: 7路红外循迹传感器模块
   - 接口: 9针排针 (VCC, GND, D1-D7)
   - 板载电阻: 10kΩ上拉电阻
   - 指示灯: 每路配有LED指示
```

### 4.2 传感器安装要求
```
【安装位置】
- 安装高度: 距离地面2-8mm
- 传感器角度: 垂直向下
- 固定方式: 螺丝固定或强力双面胶

【调试要求】
- 灵敏度调节: 通过板载电位器调节
- 检测逻辑: 黑线输出0，白线输出1
- 环境光影响: 避免强光直射
```

## 5. 常见问题排除

### 5.1 连线问题诊断
```
【问题1: 传感器无响应】
原因分析:
├── 电源问题: VCC未连接或电压不对
├── 地线问题: GND接触不良
├── 信号线问题: 信号线接错或断线
└── 传感器问题: 传感器模块损坏

解决方案:
├── 用万用表测量VCC电压 (应为3.3V)
├── 检查GND连接是否牢固
├── 逐个测试信号线连接
└── 更换传感器模块测试

【问题2: 检测不准确】
原因分析:
├── 安装高度不当: 距离地面过高或过低
├── 灵敏度设置: 电位器调节不当
├── 环境干扰: 强光或反射干扰
└── 黑线质量: 黑线不够黑或宽度不够

解决方案:
├── 调整传感器高度至2-5mm
├── 旋转电位器调节灵敏度
├── 避免强光环境，使用遮光罩
└── 使用标准黑色胶带 (宽度≥20mm)
```

### 5.2 软件调试方法
```c
//============================================================================
// 【传感器测试代码】
//============================================================================

void Sensor_Test(void)
{
    printf("=== 七路传感器测试 ===\n");
    
    while(1) {
        // 读取所有传感器状态
        printf("D1:%d D2:%d D3:%d D4:%d D5:%d D6:%d D7:%d\n", 
               D1, D2, D3, D4, D5, D6, D7);
        
        // 延时100ms
        HAL_Delay(100);
    }
}

// 【期望输出示例】
/*
传感器悬空时: D1:1 D2:1 D3:1 D4:1 D5:1 D6:1 D7:1
检测黑线时: D1:0 D2:0 D3:0 D4:0 D5:0 D6:0 D7:0
部分检测时: D1:1 D2:1 D3:0 D4:0 D5:0 D6:1 D7:1
*/
```

## 6. 性能优化建议

### 6.1 硬件优化
```
【信号质量优化】
1. 使用屏蔽线: 减少电磁干扰
2. 加装滤波电容: 在VCC和GND间加100nF电容
3. 信号线长度: 尽量缩短，建议<20cm
4. 接插件选择: 使用可靠的接插件，避免杜邦线

【机械安装优化】
1. 减震设计: 传感器安装处加减震垫
2. 防尘设计: 传感器表面加防尘罩
3. 角度调节: 设计可调节角度的安装支架
4. 高度调节: 设计可调节高度的安装机构
```

### 6.2 软件优化
```c
//============================================================================
// 【软件滤波优化】
//============================================================================

// 数字滤波函数 (已集成在代码中)
void SevenSensor_Filter(uint8_t raw[7], uint8_t filtered[7])
{
    static uint8_t history[7][3] = {0};  // 3次历史数据
    static uint8_t index = 0;

    for(int i = 0; i < 7; i++) {
        history[i][index] = raw[i];
        
        // 多数表决滤波
        uint8_t sum = history[i][0] + history[i][1] + history[i][2];
        filtered[i] = (sum >= 2) ? 1 : 0;
    }
    
    index = (index + 1) % 3;
}
```

## 7. 购买清单和成本预算

### 7.1 必需器件清单
```
【核心器件】
1. 七路循迹传感器模块 × 1
   - 型号: 7路TCRT5000红外循迹传感器
   - 价格: ¥15-25
   - 特点: 集成7个传感器，板载上拉电阻

2. 杜邦线 × 1套
   - 规格: 母对母杜邦线 20cm
   - 数量: 至少10根 (9根信号线+1根备用)
   - 价格: ¥5-8

【可选器件】
3. 排针连接器 × 1
   - 规格: 2.54mm间距 1×9P 直插排针
   - 价格: ¥2-3
   - 用途: 更可靠的连接方式

4. 面包板 × 1 (调试用)
   - 规格: 400孔面包板
   - 价格: ¥8-12
   - 用途: 临时连接和调试

总成本预算: ¥30-50
```

### 7.2 传感器模块规格书
```
【TCRT5000技术参数】
┌─────────────────┬─────────────────┐
│ 参数名称        │ 参数值          │
├─────────────────┼─────────────────┤
│ 工作电压        │ 3.3V - 5V       │
│ 工作电流        │ <20mA           │
│ 检测距离        │ 2-10mm          │
│ 检测角度        │ ±15°            │
│ 响应时间        │ <1ms            │
│ 输出类型        │ 数字信号(TTL)   │
│ 工作温度        │ -10°C ~ +50°C   │
│ 模块尺寸        │ 82×15×12mm      │
└─────────────────┴─────────────────┘
```

## 8. 实物连接示例

### 8.1 连接步骤图解
```
步骤1: 准备工作
┌─────────────────────────────────────────────────────────┐
│ 1. 准备STM32开发板                                      │
│ 2. 准备七路循迹传感器模块                               │
│ 3. 准备9根杜邦线 (建议不同颜色)                         │
│ 4. 准备万用表 (用于测试)                                │
└─────────────────────────────────────────────────────────┘

步骤2: 电源连接 (最重要)
┌─────────────────────────────────────────────────────────┐
│ 红线: 传感器VCC → STM32的3.3V                           │
│ 黑线: 传感器GND → STM32的GND                            │
│                                                         │
│ ⚠️ 务必先连接电源线，再连接信号线                       │
│ ⚠️ 确认电压为3.3V，不要接错5V                           │
└─────────────────────────────────────────────────────────┘

步骤3: 信号线连接 (按颜色区分)
┌─────────────────────────────────────────────────────────┐
│ 白线: D1 → PA0    (最左传感器)                          │
│ 灰线: D2 → PA1    (左2传感器)                           │
│ 紫线: D3 → PG4    (左1传感器)                           │
│ 蓝线: D4 → PG8    (中间传感器)                          │
│ 绿线: D5 → PG6    (右1传感器)                           │
│ 黄线: D6 → PA2    (右2传感器)                           │
│ 橙线: D7 → PA3    (最右传感器)                          │
└─────────────────────────────────────────────────────────┘
```

### 8.2 连接质量检查
```
【连接质量检查清单】
┌─────────────────────────────────────────────────────────┐
│ □ 电源连接牢固，无松动                                  │
│ □ 信号线连接正确，无接错                                │
│ □ 无短路现象 (相邻引脚不接触)                           │
│ □ 杜邦线插入到位，接触良好                              │
│ □ 传感器模块固定牢固                                    │
│ □ 线缆整理整齐，无缠绕                                  │
└─────────────────────────────────────────────────────────┘

【电气测试清单】
┌─────────────────────────────────────────────────────────┐
│ □ VCC电压测试: 3.3V ± 0.1V                              │
│ □ GND连接测试: 0V                                       │
│ □ 信号线悬空测试: 3.3V (上拉状态)                       │
│ □ 信号线接地测试: 0V (检测状态)                         │
│ □ 无短路测试: 相邻引脚间阻抗>1MΩ                        │
└─────────────────────────────────────────────────────────┘
```

## 9. 调试和测试

### 9.1 基础功能测试
```c
//============================================================================
// 【基础功能测试程序】
//============================================================================

void Basic_Sensor_Test(void)
{
    printf("=== 七路传感器基础测试 ===\n");
    printf("请将传感器悬空，观察输出...\n");

    for(int i = 0; i < 10; i++) {
        printf("测试%d: D1:%d D2:%d D3:%d D4:%d D5:%d D6:%d D7:%d\n",
               i+1, D1, D2, D3, D4, D5, D6, D7);
        HAL_Delay(500);
    }

    printf("\n请用手指遮挡各传感器，观察变化...\n");

    for(int i = 0; i < 20; i++) {
        printf("遮挡测试%d: D1:%d D2:%d D3:%d D4:%d D5:%d D6:%d D7:%d\n",
               i+1, D1, D2, D3, D4, D5, D6, D7);
        HAL_Delay(500);
    }
}

// 【期望结果】
/*
悬空状态: 所有传感器应输出1
遮挡状态: 被遮挡的传感器应输出0
*/
```

### 9.2 循迹功能测试
```c
//============================================================================
// 【循迹功能测试程序】
//============================================================================

void Tracking_Function_Test(void)
{
    printf("=== 循迹功能测试 ===\n");
    printf("请将传感器放置在黑线上进行测试...\n");

    while(1) {
        // 读取传感器状态
        uint8_t sensors[7] = {D1, D2, D3, D4, D5, D6, D7};

        // 显示传感器状态
        printf("传感器状态: ");
        for(int i = 0; i < 7; i++) {
            printf("%d ", sensors[i]);
        }

        // 调用循迹算法
        xunxian_7();

        // 显示电机状态
        printf("| 电机: L=%d R=%d\n",
               front_left_speed_duty, front_right_speed_duty);

        HAL_Delay(100);
    }
}
```

## 10. 维护和故障排除

### 10.1 日常维护
```
【定期检查项目】
1. 每周检查:
   ├── 连接线是否松动
   ├── 传感器表面是否清洁
   ├── 安装支架是否牢固
   └── 指示灯是否正常

2. 每月检查:
   ├── 传感器灵敏度是否正常
   ├── 电源电压是否稳定
   ├── 信号线是否老化
   └── 整体性能是否下降

【清洁保养】
- 传感器表面: 用无水酒精轻擦
- 连接端子: 用橡皮擦清洁氧化层
- 线缆整理: 定期整理，避免缠绕
```

### 10.2 故障快速诊断表
```
┌─────────────────┬─────────────────┬─────────────────┐
│ 故障现象        │ 可能原因        │ 解决方法        │
├─────────────────┼─────────────────┼─────────────────┤
│ 所有传感器无响应│ 电源未连接      │ 检查VCC/GND连接 │
│ 部分传感器无响应│ 信号线断线      │ 检查对应信号线  │
│ 检测不稳定      │ 连接松动        │ 重新插紧连接线  │
│ 误检测频繁      │ 环境光干扰      │ 调节灵敏度      │
│ 检测距离过近    │ 灵敏度过低      │ 调节电位器      │
│ 检测距离过远    │ 灵敏度过高      │ 调节电位器      │
│ 左右检测不对称  │ 安装角度问题    │ 调整安装角度    │
│ 响应速度慢      │ 滤波参数过大    │ 调整软件滤波    │
└─────────────────┴─────────────────┴─────────────────┘
```

---
**文档状态**: 已完成
**连线复杂度**: 简单 (9根线)
**预计连线时间**: 30分钟
**总成本**: ¥30-50
**负责人**: Bob (架构师)
**验证要求**: 必须进行连接测试和功能验证
