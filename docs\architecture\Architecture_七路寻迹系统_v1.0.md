# 七路寻迹系统架构设计文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-31
- **负责人**: Bob (架构师)
- **项目**: STM32七路寻迹系统架构设计
- **基于PRD**: PRD_七路寻迹模块升级方案_v1.0.md

## 1. 系统架构概览

### 1.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    七路寻迹系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 循迹控制器   │  │ 状态机管理   │  │ 调试接口     │         │
│  │ TrackCtrl   │  │ StateMgr    │  │ DebugIF     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  算法层 (Algorithm Layer)                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 传感器融合   │  │ PID控制器    │  │ 速度控制     │         │
│  │ SensorFusion│  │ PIDCtrl     │  │ SpeedCtrl   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  驱动层 (Driver Layer)                                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 传感器驱动   │  │ 电机驱动     │  │ 定时器驱动   │         │
│  │ SensorDrv   │  │ MotorDrv    │  │ TimerDrv    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (HAL Layer)                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ GPIO HAL    │  │ Timer HAL   │  │ UART HAL    │         │
│  │             │  │             │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心设计原则
1. **模块化设计**: 各功能模块独立，便于测试和维护
2. **分层架构**: 清晰的分层结构，降低耦合度
3. **实时性保证**: 确保5ms检测周期的硬实时要求
4. **资源优化**: 最小化内存和CPU占用
5. **向后兼容**: 保持与现有三路系统的兼容性

## 2. 硬件架构设计

### 2.1 GPIO资源分配

#### 2.1.1 传感器GPIO映射
```c
// 七路传感器GPIO配置
typedef struct {
    GPIO_TypeDef* port;
    uint16_t pin;
    char name[8];
} SensorGPIO_t;

const SensorGPIO_t sensor_gpio[7] = {
    {GPIOA, GPIO_Pin_0, "S0_L3"},  // 最左传感器
    {GPIOA, GPIO_Pin_1, "S1_L2"},  // 左2传感器  
    {GPIOG, GPIO_Pin_4, "S2_L1"},  // 左1传感器(原左)
    {GPIOG, GPIO_Pin_8, "S3_M"},   // 中间传感器(原中)
    {GPIOG, GPIO_Pin_6, "S4_R1"},  // 右1传感器(原右)
    {GPIOA, GPIO_Pin_2, "S5_R2"},  // 右2传感器
    {GPIOA, GPIO_Pin_3, "S6_R3"}   // 最右传感器
};
```

#### 2.1.2 GPIO冲突分析
| GPIO引脚 | 原用途 | 新用途 | 冲突状态 | 解决方案 |
|----------|--------|--------|----------|----------|
| PA0 | 未使用 | S0传感器 | 无冲突 | 直接使用 |
| PA1 | 未使用 | S1传感器 | 无冲突 | 直接使用 |
| PA2 | 未使用 | S5传感器 | 无冲突 | 直接使用 |
| PA3 | 未使用 | S6传感器 | 无冲突 | 直接使用 |
| PG4 | 左传感器 | S2传感器 | 无冲突 | 保持原用途 |
| PG6 | 右传感器 | S4传感器 | 无冲突 | 保持原用途 |
| PG8 | 中传感器 | S3传感器 | 无冲突 | 保持原用途 |

### 2.2 内存资源规划

#### 2.2.1 数据结构内存占用
```c
// 主要数据结构内存分析
typedef struct {
    uint8_t sensor_raw[7];          // 7字节 - 原始数据
    uint8_t sensor_filtered[7];     // 7字节 - 滤波数据
    uint8_t sensor_history[7][4];   // 28字节 - 历史数据
    int16_t line_position;          // 2字节 - 黑线位置
    uint8_t line_detected;          // 1字节 - 检测标志
    uint8_t sensor_count;           // 1字节 - 传感器计数
    uint8_t track_state;            // 1字节 - 循迹状态
    uint8_t reserved;               // 1字节 - 对齐保留
} SevenLineSensor_t;                // 总计: 48字节

typedef struct {
    float kp, ki, kd;               // 12字节 - PID参数
    int16_t last_error;             // 2字节 - 上次误差
    int32_t integral;               // 4字节 - 积分项
    int16_t output;                 // 2字节 - 输出
    int16_t output_limit;           // 2字节 - 输出限制
    uint8_t enabled;                // 1字节 - 使能标志
    uint8_t reserved;               // 1字节 - 对齐保留
} PID_Controller_t;                 // 总计: 24字节

// 总内存增加: 48 + 24 = 72字节 (符合<100字节要求)
```

### 2.3 时序架构设计

#### 2.3.1 主循环时序图
```
时间轴 (5ms周期):
0ms    1ms    2ms    3ms    4ms    5ms
│      │      │      │      │      │
├─传感器读取─┤      │      │      │
│      ├─数据滤波─┤  │      │      │
│      │      ├─位置计算─┤  │      │
│      │      │      ├─PID计算─┤  │
│      │      │      │      ├─电机控制─┤
│      │      │      │      │      │
└──────┴──────┴──────┴──────┴──────┘
```

#### 2.3.2 中断优先级设计
```c
// 中断优先级配置
#define TIM2_IRQ_PRIORITY       0   // 最高优先级 - 主时序
#define USART3_IRQ_PRIORITY     2   // 中等优先级 - 串口通信
#define EXTI_IRQ_PRIORITY       3   // 低优先级 - 外部中断
```

## 3. 软件架构设计

### 3.1 模块化架构

#### 3.1.1 传感器管理模块
```c
// 传感器管理模块接口
typedef struct {
    void (*init)(void);                           // 初始化
    void (*read_raw)(uint8_t data[7]);           // 读取原始数据
    void (*filter)(uint8_t raw[7], uint8_t filtered[7]); // 数据滤波
    int16_t (*calc_position)(uint8_t data[7]);   // 位置计算
    uint8_t (*get_pattern)(uint8_t data[7]);     // 获取模式
} SensorManager_t;

// 传感器管理器实例
extern SensorManager_t g_sensor_mgr;
```

#### 3.1.2 控制算法模块
```c
// PID控制器模块接口
typedef struct {
    void (*init)(PID_Controller_t* pid, float kp, float ki, float kd);
    int16_t (*calculate)(PID_Controller_t* pid, int16_t error);
    void (*reset)(PID_Controller_t* pid);
    void (*set_limits)(PID_Controller_t* pid, int16_t limit);
} PIDController_t;

// 速度控制模块接口
typedef struct {
    void (*init)(void);
    void (*set_base_speed)(uint8_t speed);
    void (*adaptive_control)(SevenLineSensor_t* sensor);
    void (*apply_correction)(int16_t correction);
} SpeedController_t;
```

#### 3.1.3 状态机模块
```c
// 状态机模块接口
typedef enum {
    TRACK_NORMAL = 0,
    TRACK_SHARP_LEFT,
    TRACK_SHARP_RIGHT,
    TRACK_LOST,
    TRACK_INTERSECTION,
    TRACK_END
} TrackState_t;

typedef struct {
    void (*init)(void);
    TrackState_t (*update)(SevenLineSensor_t* sensor);
    void (*handle_state)(TrackState_t state);
    const char* (*get_state_name)(TrackState_t state);
} StateMachine_t;
```

### 3.2 算法架构

#### 3.2.1 传感器数据融合算法
```c
// 加权平均位置计算 (优化版)
int16_t CalculateLinePosition_Optimized(uint8_t sensors[7]) {
    // 使用查表法提高效率
    static const int16_t position_weights[7] = {
        -3000, -2000, -1000, 0, 1000, 2000, 3000
    };
    
    int32_t weighted_sum = 0;
    uint8_t active_count = 0;
    
    // 展开循环提高效率
    if(sensors[0]) { weighted_sum += position_weights[0]; active_count++; }
    if(sensors[1]) { weighted_sum += position_weights[1]; active_count++; }
    if(sensors[2]) { weighted_sum += position_weights[2]; active_count++; }
    if(sensors[3]) { weighted_sum += position_weights[3]; active_count++; }
    if(sensors[4]) { weighted_sum += position_weights[4]; active_count++; }
    if(sensors[5]) { weighted_sum += position_weights[5]; active_count++; }
    if(sensors[6]) { weighted_sum += position_weights[6]; active_count++; }
    
    return (active_count > 0) ? (weighted_sum / active_count) : 0;
}
```

#### 3.2.2 自适应PID控制算法
```c
// 自适应PID控制器
typedef struct {
    PID_Controller_t base_pid;      // 基础PID
    float adaptive_kp[3];           // 自适应Kp参数
    float adaptive_ki[3];           // 自适应Ki参数
    float adaptive_kd[3];           // 自适应Kd参数
    uint8_t current_mode;           // 当前模式
} AdaptivePID_t;

// 根据误差大小选择PID参数
void AdaptivePID_Update(AdaptivePID_t* apid, int16_t error) {
    uint16_t abs_error = abs(error);
    
    if(abs_error < 500) {           // 小误差 - 精细控制
        apid->current_mode = 0;
        apid->base_pid.kp = apid->adaptive_kp[0];
        apid->base_pid.ki = apid->adaptive_ki[0];
        apid->base_pid.kd = apid->adaptive_kd[0];
    } else if(abs_error < 1500) {   // 中误差 - 平衡控制
        apid->current_mode = 1;
        apid->base_pid.kp = apid->adaptive_kp[1];
        apid->base_pid.ki = apid->adaptive_ki[1];
        apid->base_pid.kd = apid->adaptive_kd[1];
    } else {                        // 大误差 - 快速响应
        apid->current_mode = 2;
        apid->base_pid.kp = apid->adaptive_kp[2];
        apid->base_pid.ki = apid->adaptive_ki[2];
        apid->base_pid.kd = apid->adaptive_kd[2];
    }
}
```

### 3.3 数据流架构

#### 3.3.1 数据流图
```
传感器原始数据 → 数字滤波 → 位置计算 → PID控制 → 电机输出
     ↓              ↓           ↓          ↓         ↓
   历史缓存 → 状态判断 → 速度调节 → 输出限制 → PWM生成
     ↓              ↓           ↓          ↓         ↓
   调试输出 → 异常检测 → 参数调整 → 安全保护 → 状态反馈
```

#### 3.3.2 数据处理管道
```c
// 数据处理管道
typedef struct {
    uint8_t raw_data[7];        // 原始数据
    uint8_t filtered_data[7];   // 滤波数据
    int16_t line_position;      // 计算位置
    int16_t position_error;     // 位置误差
    int16_t pid_output;         // PID输出
    int16_t motor_left;         // 左电机输出
    int16_t motor_right;        // 右电机输出
} DataPipeline_t;

// 数据处理流程
void ProcessDataPipeline(DataPipeline_t* pipeline) {
    // 1. 读取传感器数据
    g_sensor_mgr.read_raw(pipeline->raw_data);
    
    // 2. 数据滤波
    g_sensor_mgr.filter(pipeline->raw_data, pipeline->filtered_data);
    
    // 3. 位置计算
    pipeline->line_position = g_sensor_mgr.calc_position(pipeline->filtered_data);
    
    // 4. 误差计算
    pipeline->position_error = 0 - pipeline->line_position;
    
    // 5. PID控制
    pipeline->pid_output = g_pid_ctrl.calculate(&g_main_pid, pipeline->position_error);
    
    // 6. 电机输出计算
    CalculateMotorOutput(pipeline);
}
```

## 4. 接口设计

### 4.1 硬件接口层

#### 4.1.1 传感器接口
```c
// 传感器硬件接口
#define SENSOR_COUNT 7

// 传感器GPIO读取宏
#define READ_SENSOR_0() GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0)
#define READ_SENSOR_1() GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1)
#define READ_SENSOR_2() GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_4)
#define READ_SENSOR_3() GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_8)
#define READ_SENSOR_4() GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_6)
#define READ_SENSOR_5() GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_2)
#define READ_SENSOR_6() GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_3)

// 批量读取函数
void ReadAllSensors(uint8_t data[7]) {
    data[0] = READ_SENSOR_0();
    data[1] = READ_SENSOR_1();
    data[2] = READ_SENSOR_2();
    data[3] = READ_SENSOR_3();
    data[4] = READ_SENSOR_4();
    data[5] = READ_SENSOR_5();
    data[6] = READ_SENSOR_6();
}
```

#### 4.1.2 电机接口兼容层
```c
// 电机控制兼容接口
typedef struct {
    void (*go)(void);           // 前进
    void (*back)(void);         // 后退
    void (*left)(void);         // 左转
    void (*right)(void);        // 右转
    void (*stop)(void);         // 停止
    void (*set_speed)(int16_t left, int16_t right); // 差速控制
} MotorInterface_t;

// 新增差速控制函数
void CarDifferentialControl(int16_t left_speed, int16_t right_speed) {
    // 基于PID输出的差速控制
    front_left_speed_duty = SPEED_DUTY + left_speed;
    front_right_speed_duty = SPEED_DUTY + right_speed;
    behind_left_speed_duty = SPEED_DUTY + left_speed;
    behind_right_speed_duty = SPEED_DUTY + right_speed;
    
    // 速度限制
    LIMIT_SPEED(front_left_speed_duty);
    LIMIT_SPEED(front_right_speed_duty);
    LIMIT_SPEED(behind_left_speed_duty);
    LIMIT_SPEED(behind_right_speed_duty);
}
```

### 4.2 软件接口层

#### 4.2.1 配置接口
```c
// 系统配置结构
typedef struct {
    // PID参数配置
    float pid_kp;
    float pid_ki;
    float pid_kd;
    
    // 速度配置
    uint8_t base_speed;
    uint8_t turn_speed;
    uint8_t low_speed;
    
    // 传感器配置
    uint8_t filter_depth;
    uint16_t detection_threshold;
    
    // 调试配置
    uint8_t debug_enabled;
    uint8_t uart_output;
} SystemConfig_t;

// 配置管理接口
void LoadDefaultConfig(SystemConfig_t* config);
void SaveConfig(const SystemConfig_t* config);
void ApplyConfig(const SystemConfig_t* config);
```

#### 4.2.2 调试接口
```c
// 调试数据结构
typedef struct {
    uint32_t timestamp;         // 时间戳
    uint8_t sensor_data[7];     // 传感器数据
    int16_t line_position;      // 黑线位置
    int16_t pid_output;         // PID输出
    uint8_t track_state;        // 循迹状态
    int16_t motor_left;         // 左电机输出
    int16_t motor_right;        // 右电机输出
} DebugData_t;

// 调试接口函数
void DebugInit(void);
void DebugLog(const DebugData_t* data);
void DebugPrint(const char* format, ...);
void DebugDumpSensorData(void);
```

## 5. 性能优化策略

### 5.1 计算优化

#### 5.1.1 定点运算优化
```c
// 使用定点运算替代浮点运算
#define FIXED_POINT_SCALE 1000

typedef int32_t fixed_t;

// 定点PID控制器
typedef struct {
    fixed_t kp, ki, kd;         // 定点PID参数
    int16_t last_error;
    int32_t integral;
    int16_t output;
} FixedPID_t;

// 定点PID计算
int16_t FixedPID_Calculate(FixedPID_t* pid, int16_t error) {
    // 比例项 (定点运算)
    int32_t proportional = (pid->kp * error) / FIXED_POINT_SCALE;
    
    // 积分项
    pid->integral += error;
    int32_t integral = (pid->ki * pid->integral) / FIXED_POINT_SCALE;
    
    // 微分项
    int32_t derivative = (pid->kd * (error - pid->last_error)) / FIXED_POINT_SCALE;
    pid->last_error = error;
    
    // 输出计算
    int32_t output = proportional + integral + derivative;
    
    // 限幅
    if(output > 1000) output = 1000;
    if(output < -1000) output = -1000;
    
    return (int16_t)output;
}
```

#### 5.1.2 查表法优化
```c
// 传感器模式查表
static const TrackState_t pattern_lookup[128] = {
    // 预计算的模式映射表
    [0b0000000] = TRACK_LOST,
    [0b0001000] = TRACK_NORMAL,
    [0b0011000] = TRACK_NORMAL,
    [0b1110000] = TRACK_SHARP_LEFT,
    [0b0000111] = TRACK_SHARP_RIGHT,
    [0b1111111] = TRACK_INTERSECTION,
    // ... 其他模式
};

// 快速状态查找
TrackState_t GetTrackStateFromPattern(uint8_t pattern) {
    return pattern_lookup[pattern & 0x7F];
}
```

### 5.2 内存优化

#### 5.2.1 数据结构对齐
```c
// 内存对齐优化的数据结构
typedef struct __attribute__((packed)) {
    uint8_t sensor_raw[7];      // 7字节
    uint8_t padding1;           // 1字节对齐
    int16_t line_position;      // 2字节
    uint8_t line_detected;      // 1字节
    uint8_t sensor_count;       // 1字节
    uint8_t track_state;        // 1字节
    uint8_t padding2;           // 1字节对齐
} OptimizedSensorData_t;        // 总计: 16字节 (4字节对齐)
```

#### 5.2.2 缓冲区管理
```c
// 环形缓冲区用于历史数据
#define HISTORY_DEPTH 4
typedef struct {
    uint8_t data[7][HISTORY_DEPTH];
    uint8_t write_index;
    uint8_t valid_count;
} SensorHistory_t;

// 高效的历史数据更新
void UpdateSensorHistory(SensorHistory_t* hist, const uint8_t new_data[7]) {
    uint8_t idx = hist->write_index;
    memcpy(hist->data[idx], new_data, 7);
    
    hist->write_index = (idx + 1) % HISTORY_DEPTH;
    if(hist->valid_count < HISTORY_DEPTH) {
        hist->valid_count++;
    }
}
```

## 6. 风险评估与缓解

### 6.1 技术风险

#### 6.1.1 实时性风险
**风险**: 七路传感器处理可能超出5ms时间限制
**缓解措施**:
- 使用定点运算替代浮点运算
- 优化算法复杂度，使用查表法
- 分时处理非关键任务

#### 6.1.2 内存不足风险
**风险**: 新增数据结构可能导致内存不足
**缓解措施**:
- 精确计算内存需求(72字节)
- 使用内存对齐优化
- 移除不必要的历史数据

### 6.2 集成风险

#### 6.2.1 兼容性风险
**风险**: 与现有代码不兼容
**缓解措施**:
- 保持现有接口不变
- 渐进式集成策略
- 完整的回归测试

#### 6.2.2 性能退化风险
**风险**: 新系统性能不如原系统
**缓解措施**:
- 性能基准测试
- 分阶段性能验证
- 可配置的算法复杂度

## 7. 测试架构

### 7.1 单元测试框架
```c
// 单元测试框架
typedef struct {
    const char* test_name;
    void (*test_func)(void);
    uint8_t passed;
} UnitTest_t;

// 测试用例定义
void Test_SensorReading(void);
void Test_PositionCalculation(void);
void Test_PIDController(void);
void Test_StateMachine(void);

// 测试套件
const UnitTest_t test_suite[] = {
    {"Sensor Reading", Test_SensorReading, 0},
    {"Position Calculation", Test_PositionCalculation, 0},
    {"PID Controller", Test_PIDController, 0},
    {"State Machine", Test_StateMachine, 0}
};
```

### 7.2 集成测试策略
1. **模块集成测试**: 逐步集成各功能模块
2. **系统集成测试**: 完整系统功能验证
3. **性能集成测试**: 实时性和精度验证
4. **压力测试**: 极限条件下的稳定性测试

---
**文档创建时间**: 2025-01-31  
**负责人**: Bob (架构师)  
**技术评审状态**: 待评审  
**下一步**: 详细实现计划和代码开发
