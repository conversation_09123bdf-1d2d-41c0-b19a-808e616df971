# STM32电机控制模块详解

## 文档信息
- **文档标题**: 电机控制模块详解
- **创建日期**: 2025-01-31
- **负责人**: Alex (工程师)
- **版本**: v1.0
- **项目**: 3路循迹STM32参考实验 - 电机控制模块解析

## 1. 电机控制系统架构

### 1.1 硬件架构总览
```
电机控制系统架构
├── PWM控制器
│   ├── 软件PWM实现
│   ├── 占空比控制
│   └── 方向控制
├── 四轮驱动系统
│   ├── 前左电机 (front_left)
│   ├── 前右电机 (front_right)
│   ├── 后左电机 (behind_left)
│   └── 后右电机 (behind_right)
└── 运动控制接口
    ├── 基础运动函数
    ├── 差速控制函数
    └── 速度调节函数
```

### 1.2 电机控制变量系统
```c
//============================================================================
// 【电机控制核心变量】
//============================================================================

// 【电机速度控制变量】
extern char front_left_speed_duty;    // 前左电机占空比 (-50 ~ +50)
extern char front_right_speed_duty;   // 前右电机占空比 (-50 ~ +50)
extern char behind_left_speed_duty;   // 后左电机占空比 (-50 ~ +50)
extern char behind_right_speed_duty;  // 后右电机占空比 (-50 ~ +50)

// 【PWM时序控制】
extern unsigned int speed_count;      // PWM计数器 (0-49, 50为一个周期)

// 【速度参数定义】
#define SPEED_DUTY 40                 // 默认占空比 (40/50 = 80%功率)
#define MAX_SPEED_DUTY 50             // 最大占空比 (100%功率)
#define MIN_SPEED_DUTY -50            // 最小占空比 (反向100%功率)

// 【电机状态标志】
typedef enum {
    MOTOR_STOP = 0,        // 停止
    MOTOR_FORWARD,         // 前进
    MOTOR_BACKWARD,        // 后退
    MOTOR_BRAKE            // 刹车
} MotorState_t;
```

## 2. PWM控制系统详解

### 2.1 软件PWM实现原理
```c
//============================================================================
// 【软件PWM实现机制】
//============================================================================

// 【PWM周期管理】
void PWM_TimerHandler(void)  // 定时器中断处理函数
{
    speed_count++;           // PWM计数器递增
    
    if(speed_count >= 50) {  // 一个PWM周期完成
        speed_count = 0;     // 重置计数器
    }
    
    CarMove();               // 执行电机控制
}

// 【PWM占空比计算】
/*
PWM周期 = 50个计数单位
占空比 = (duty_value / 50) × 100%

示例:
- duty = 40: 占空比 = 40/50 = 80%
- duty = 25: 占空比 = 25/50 = 50%
- duty = 10: 占空比 = 10/50 = 20%
*/

// 【PWM频率计算】
/*
假设定时器中断频率为 1kHz:
PWM频率 = 1000Hz / 50 = 20Hz
PWM周期 = 50ms

这个频率适合直流电机控制，避免电机抖动
*/
```

### 2.2 电机控制核心函数
```c
//============================================================================
// 【电机控制核心函数 - CarMove()】
//============================================================================

void CarMove(void)
{   
    // 【后右电机特殊处理】
    BEHIND_RIGHT_EN;  // 硬件使能信号
    
    // 【前右电机PWM控制】
    if(front_right_speed_duty > 0) {        // 正向旋转
        if(speed_count < front_right_speed_duty) {
            FRONT_RIGHT_GO;                  // 电机正转
        } else {
            FRONT_RIGHT_STOP;               // 电机停止
        }
    }
    else if(front_right_speed_duty < 0) {   // 反向旋转
        if(speed_count < (-1) * front_right_speed_duty) {
            FRONT_RIGHT_BACK;               // 电机反转
        } else {
            FRONT_RIGHT_STOP;               // 电机停止
        }
    }
    else {
        FRONT_RIGHT_STOP;                   // 占空比为0，停止
    }
    
    // 【后左电机PWM控制】
    if(behind_left_speed_duty > 0) {        // 正向旋转
        if(speed_count < behind_left_speed_duty) {
            BEHIND_LEFT_GO;                  // 电机正转
        } else {
            BEHIND_LEFT_STOP;               // 电机停止
        }
    }
    else if(behind_left_speed_duty < 0) {   // 反向旋转
        if(speed_count < (-1) * behind_left_speed_duty) {
            BEHIND_LEFT_BACK;               // 电机反转
        } else {
            BEHIND_LEFT_STOP;               // 电机停止
        }
    }
    else {
        BEHIND_LEFT_STOP;                   // 占空比为0，停止
    }
    
    // 注意: 前左和后右电机控制代码在原实现中被注释
    // 可能是由于硬件设计或驱动板限制
}
```

### 2.3 电机硬件接口定义
```c
//============================================================================
// 【电机硬件接口宏定义】
//============================================================================

// 【前右电机控制接口】
#define FRONT_RIGHT_GO    GPIO_SetBits(GPIOE, GPIO_Pin_2)     // 前右电机正转
#define FRONT_RIGHT_BACK  GPIO_SetBits(GPIOE, GPIO_Pin_3)     // 前右电机反转
#define FRONT_RIGHT_STOP  do{GPIO_ResetBits(GPIOE, GPIO_Pin_2); \
                             GPIO_ResetBits(GPIOE, GPIO_Pin_3);}while(0)

// 【后左电机控制接口】
#define BEHIND_LEFT_GO    GPIO_SetBits(GPIOE, GPIO_Pin_4)     // 后左电机正转
#define BEHIND_LEFT_BACK  GPIO_SetBits(GPIOE, GPIO_Pin_5)     // 后左电机反转
#define BEHIND_LEFT_STOP  do{GPIO_ResetBits(GPIOE, GPIO_Pin_4); \
                             GPIO_ResetBits(GPIOE, GPIO_Pin_5);}while(0)

// 【后右电机使能】
#define BEHIND_RIGHT_EN   GPIO_SetBits(GPIOE, GPIO_Pin_6)     // 后右电机使能

// 【前左电机控制接口】(在原代码中被注释)
// #define FRONT_LEFT_GO    GPIO_SetBits(GPIOE, GPIO_Pin_0)
// #define FRONT_LEFT_BACK  GPIO_SetBits(GPIOE, GPIO_Pin_1)
// #define FRONT_LEFT_STOP  do{GPIO_ResetBits(GPIOE, GPIO_Pin_0); \
//                             GPIO_ResetBits(GPIOE, GPIO_Pin_1);}while(0)

// 【后右电机控制接口】(在原代码中被注释)
// #define BEHIND_RIGHT_GO   GPIO_SetBits(GPIOE, GPIO_Pin_7)
// #define BEHIND_RIGHT_BACK GPIO_SetBits(GPIOE, GPIO_Pin_8)
// #define BEHIND_RIGHT_STOP do{GPIO_ResetBits(GPIOE, GPIO_Pin_7); \
//                              GPIO_ResetBits(GPIOE, GPIO_Pin_8);}while(0)
```

## 3. 基础运动控制函数

### 3.1 直线运动控制
```c
//============================================================================
// 【直线运动控制函数】
//============================================================================

// 【前进控制】
void CarGo(void)
{
    // 【双电机驱动策略】
    front_right_speed_duty = SPEED_DUTY;   // 前右电机: 80%功率前进
    behind_left_speed_duty = SPEED_DUTY;   // 后左电机: 80%功率前进
    
    // 【未使用电机清零】
    front_left_speed_duty = 0;             // 前左电机: 禁用
    behind_right_speed_duty = 0;           // 后右电机: 禁用
    
    // 【运动分析】
    // 采用对角线驱动方式: 前右 + 后左
    // 优点: 减少功耗，简化控制
    // 缺点: 驱动力不如四轮驱动
}

// 【后退控制】
void CarBack(void)
{
    // 【四电机反向驱动】
    front_left_speed_duty = -SPEED_DUTY;   // 前左电机: 反向
    front_right_speed_duty = -SPEED_DUTY;  // 前右电机: 反向
    behind_left_speed_duty = -SPEED_DUTY;  // 后左电机: 反向
    behind_right_speed_duty = -SPEED_DUTY; // 后右电机: 反向
    
    // 【运动分析】
    // 四轮同时反转，提供最大后退力
    // 适用于需要快速后退的场景
}

// 【停止控制】
void CarStop(void)
{
    // 【所有电机停止】
    front_left_speed_duty = 0;
    front_right_speed_duty = 0;
    behind_left_speed_duty = 0;
    behind_right_speed_duty = 0;
    
    // 【停止方式】
    // 自由停止: 电机断电，依靠摩擦力停止
    // 优点: 节能，对电机冲击小
    // 缺点: 停止距离较长
}
```

### 3.2 转向运动控制
```c
//============================================================================
// 【转向运动控制函数】
//============================================================================

// 【左转控制】
void CarLeft(void)
{
    // 【差速转向策略】
    front_left_speed_duty = -20;           // 前左: 低速反转
    front_right_speed_duty = SPEED_DUTY;   // 前右: 正常前进
    behind_left_speed_duty = -20;          // 后左: 低速反转
    behind_right_speed_duty = SPEED_DUTY + 10; // 后右: 增强前进
    
    // 【转向分析】
    // 左侧电机反转，右侧电机前进
    // 形成逆时针力矩，实现左转
    // 后右电机增强(+10)提供额外转向力矩
}

// 【右转控制】
void CarRight(void)
{
    // 【差速转向策略】
    front_left_speed_duty = SPEED_DUTY;    // 前左: 正常前进
    front_right_speed_duty = -20;          // 前右: 低速反转
    behind_left_speed_duty = SPEED_DUTY + 10; // 后左: 增强前进
    behind_right_speed_duty = -20;         // 后右: 低速反转
    
    // 【转向分析】
    // 右侧电机反转，左侧电机前进
    // 形成顺时针力矩，实现右转
    // 后左电机增强(+10)提供额外转向力矩
}
```

## 4. 高级电机控制

### 4.1 差速控制系统
```c
//============================================================================
// 【差速控制系统】
//============================================================================

// 【七路循迹差速控制】
void SevenTrack_MotorControl(int16_t correction)
{
    // 【基础速度设定】
    int16_t base_speed = SPEED_DUTY;       // 基础速度: 40 (80%功率)
    
    // 【差速计算】
    int16_t left_speed = base_speed - correction;   // 左侧电机速度
    int16_t right_speed = base_speed + correction;  // 右侧电机速度
    
    // 【速度限幅保护】
    if(left_speed > MAX_SPEED_DUTY) left_speed = MAX_SPEED_DUTY;
    if(left_speed < MIN_SPEED_DUTY) left_speed = MIN_SPEED_DUTY;
    if(right_speed > MAX_SPEED_DUTY) right_speed = MAX_SPEED_DUTY;
    if(right_speed < MIN_SPEED_DUTY) right_speed = MIN_SPEED_DUTY;
    
    // 【电机速度分配】
    front_left_speed_duty = left_speed;
    front_right_speed_duty = right_speed;
    behind_left_speed_duty = left_speed;
    behind_right_speed_duty = right_speed;
}

// 【差速控制原理详解】
/*
correction值的含义:
- correction > 0: 黑线偏右，需要左转修正
  * 左侧电机减速: base_speed - correction
  * 右侧电机加速: base_speed + correction
  * 结果: 小车向左转，修正偏右

- correction < 0: 黑线偏左，需要右转修正
  * 左侧电机加速: base_speed - correction (实际增加)
  * 右侧电机减速: base_speed + correction (实际减少)
  * 结果: 小车向右转，修正偏左

- correction = 0: 黑线居中，直行
  * 左右电机同速: base_speed
  * 结果: 小车直行

示例计算:
base_speed = 40, correction = 10
left_speed = 40 - 10 = 30
right_speed = 40 + 10 = 50
结果: 左转修正
*/
```

### 4.2 速度控制优化
```c
//============================================================================
// 【速度控制优化策略】
//============================================================================

// 【渐变速度控制】
void MotorControl_SmoothSpeed(int16_t target_left, int16_t target_right)
{
    static int16_t current_left = 0;
    static int16_t current_right = 0;
    
    // 【速度渐变参数】
    #define SPEED_STEP 5              // 每次调整步长
    
    // 【左电机渐变】
    if(current_left < target_left) {
        current_left += SPEED_STEP;
        if(current_left > target_left) current_left = target_left;
    } else if(current_left > target_left) {
        current_left -= SPEED_STEP;
        if(current_left < target_left) current_left = target_left;
    }
    
    // 【右电机渐变】
    if(current_right < target_right) {
        current_right += SPEED_STEP;
        if(current_right > target_right) current_right = target_right;
    } else if(current_right > target_right) {
        current_right -= SPEED_STEP;
        if(current_right < target_right) current_right = target_right;
    }
    
    // 【应用渐变速度】
    front_left_speed_duty = current_left;
    front_right_speed_duty = current_right;
    behind_left_speed_duty = current_left;
    behind_right_speed_duty = current_right;
}

// 【自适应速度控制】
void MotorControl_AdaptiveSpeed(TrackState_t track_state)
{
    switch(track_state) {
        case TRACK_NORMAL:
            // 正常循迹 - 标准速度
            base_speed = SPEED_DUTY;
            break;
            
        case TRACK_SHARP_LEFT:
        case TRACK_SHARP_RIGHT:
            // 急转弯 - 降低速度
            base_speed = SPEED_DUTY * 0.7;  // 70%速度
            break;
            
        case TRACK_INTERSECTION:
            // 交叉路口 - 低速通过
            base_speed = SPEED_DUTY * 0.5;  // 50%速度
            break;
            
        case TRACK_LOST:
            // 失去黑线 - 停止或搜索
            base_speed = 0;
            break;
            
        default:
            base_speed = SPEED_DUTY;
            break;
    }
}
```

## 5. 电机初始化系统

### 5.1 GPIO初始化配置
```c
//============================================================================
// 【电机GPIO初始化】
//============================================================================

void MotorGPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 【使能GPIOE时钟】
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);

    // 【电机控制引脚配置】
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;    // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;   // 50MHz速度

    // 【前右电机引脚】
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
    GPIO_Init(GPIOE, &GPIO_InitStructure);

    // 【后左电机引脚】
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5;
    GPIO_Init(GPIOE, &GPIO_InitStructure);

    // 【后右电机使能引脚】
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6;
    GPIO_Init(GPIOE, &GPIO_InitStructure);

    // 【初始状态设置】
    FRONT_RIGHT_STOP;   // 前右电机停止
    BEHIND_LEFT_STOP;   // 后左电机停止
    BEHIND_RIGHT_EN;    // 后右电机使能
}
```

### 5.2 定时器初始化配置
```c
//============================================================================
// 【PWM定时器初始化】
//============================================================================

void MotorPWM_TimerInit(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 【使能定时器时钟】
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    // 【定时器基础配置】
    TIM_TimeBaseStructure.TIM_Period = 999;         // 自动重装载值 (1ms)
    TIM_TimeBaseStructure.TIM_Prescaler = 71;       // 预分频器 (72MHz/72=1MHz)
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    // 【中断配置】
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);

    // 【NVIC配置】
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 【启动定时器】
    TIM_Cmd(TIM2, ENABLE);
}

// 【定时器中断处理函数】
void TIM2_IRQHandler(void)
{
    if(TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

        // 【PWM计数管理】
        PWM_TimerHandler();
    }
}
```

## 6. 电机控制接口设计

### 6.1 统一电机控制接口
```c
//============================================================================
// 【统一电机控制接口】
//============================================================================

// 【电机控制结构体】
typedef struct {
    int16_t left_speed;     // 左侧电机速度 (-50 ~ +50)
    int16_t right_speed;    // 右侧电机速度 (-50 ~ +50)
    uint8_t brake_enable;   // 刹车使能
    uint8_t smooth_enable;  // 平滑控制使能
} MotorControl_t;

// 【电机状态结构体】
typedef struct {
    MotorState_t front_left_state;
    MotorState_t front_right_state;
    MotorState_t behind_left_state;
    MotorState_t behind_right_state;
    uint16_t current_pwm_freq;
    uint32_t total_run_time;
} MotorStatus_t;

// 【统一控制接口】
HAL_StatusTypeDef Motor_SetSpeed(MotorControl_t* control);
HAL_StatusTypeDef Motor_GetStatus(MotorStatus_t* status);
HAL_StatusTypeDef Motor_Emergency_Stop(void);
HAL_StatusTypeDef Motor_SelfTest(void);
```

### 6.2 高级控制接口
```c
//============================================================================
// 【高级电机控制接口】
//============================================================================

// 【运动模式枚举】
typedef enum {
    MOTION_STOP = 0,        // 停止
    MOTION_FORWARD,         // 前进
    MOTION_BACKWARD,        // 后退
    MOTION_TURN_LEFT,       // 左转
    MOTION_TURN_RIGHT,      // 右转
    MOTION_ROTATE_LEFT,     // 原地左转
    MOTION_ROTATE_RIGHT,    // 原地右转
    MOTION_CUSTOM           // 自定义
} MotionMode_t;

// 【运动参数结构】
typedef struct {
    MotionMode_t mode;      // 运动模式
    uint8_t speed_level;    // 速度等级 (1-10)
    uint16_t duration_ms;   // 持续时间
    uint8_t auto_stop;      // 自动停止
} MotionParams_t;

// 【高级控制函数】
HAL_StatusTypeDef Motor_ExecuteMotion(MotionParams_t* params);
HAL_StatusTypeDef Motor_SetSpeedLevel(uint8_t level);
HAL_StatusTypeDef Motor_SetPWMFrequency(uint16_t freq_hz);
```

## 7. 性能分析与优化

### 7.1 电机控制性能分析
```c
//============================================================================
// 【电机控制性能指标】
//============================================================================

// 【响应时间分析】
/*
PWM更新周期: 1ms (定时器中断)
电机响应时间: ~10ms (机械惯性)
控制精度: ±2% (PWM分辨率限制)
最大转速: 取决于电机额定参数
*/

// 【资源占用分析】
/*
GPIO资源: GPIOE (6个引脚)
定时器资源: TIM2 (1个)
中断优先级: 1 (较高优先级)
内存占用: ~200字节 (全局变量)
CPU占用: <1% (1ms中断)
*/

// 【功耗分析】
/*
静态功耗: GPIO输出 ~1mA
动态功耗: 取决于电机负载
PWM控制功耗: 忽略不计
总体效率: >90% (软件PWM)
*/
```

### 7.2 电机控制优化策略
```c
//============================================================================
// 【电机控制优化策略】
//============================================================================

// 【PWM频率优化】
void Motor_OptimizePWMFreq(uint16_t motor_rpm)
{
    // 【根据电机转速调整PWM频率】
    if(motor_rpm < 100) {
        // 低速: 使用高频PWM减少抖动
        pwm_period = 25;  // 40Hz
    } else if(motor_rpm < 500) {
        // 中速: 标准频率
        pwm_period = 50;  // 20Hz
    } else {
        // 高速: 使用低频PWM减少开关损耗
        pwm_period = 100; // 10Hz
    }
}

// 【死区时间控制】
void Motor_DeadTimeControl(void)
{
    // 【防止同一电机正反转同时导通】
    static uint8_t dead_time_count = 0;

    if(motor_direction_changed) {
        dead_time_count = 3;  // 3ms死区时间
        motor_direction_changed = 0;
    }

    if(dead_time_count > 0) {
        // 死区时间内，所有电机停止
        FRONT_RIGHT_STOP;
        BEHIND_LEFT_STOP;
        dead_time_count--;
    }
}

// 【电流限制保护】
void Motor_CurrentLimit(void)
{
    // 【软件电流限制】
    static uint8_t overload_count = 0;

    if(motor_current_high) {
        overload_count++;
        if(overload_count > 10) {  // 连续10ms过载
            // 降低PWM占空比
            if(front_right_speed_duty > 0) front_right_speed_duty *= 0.8;
            if(behind_left_speed_duty > 0) behind_left_speed_duty *= 0.8;
            overload_count = 0;
        }
    } else {
        overload_count = 0;
    }
}
```

## 8. 故障诊断与保护

### 8.1 故障检测系统
```c
//============================================================================
// 【电机故障检测】
//============================================================================

// 【故障类型枚举】
typedef enum {
    MOTOR_FAULT_NONE = 0,
    MOTOR_FAULT_OVERCURRENT,    // 过流
    MOTOR_FAULT_OVERHEAT,       // 过热
    MOTOR_FAULT_STALL,          // 堵转
    MOTOR_FAULT_DISCONNECT,     // 断线
    MOTOR_FAULT_PWM_ERROR       // PWM错误
} MotorFault_t;

// 【故障检测函数】
MotorFault_t Motor_FaultDetection(void)
{
    // 【PWM计数器检查】
    if(speed_count > 60) {  // 异常计数值
        return MOTOR_FAULT_PWM_ERROR;
    }

    // 【电机响应检查】
    static uint8_t no_response_count = 0;
    if(motor_speed_duty != 0 && motor_actual_speed == 0) {
        no_response_count++;
        if(no_response_count > 100) {  // 100ms无响应
            return MOTOR_FAULT_STALL;
        }
    } else {
        no_response_count = 0;
    }

    return MOTOR_FAULT_NONE;
}

// 【故障处理函数】
void Motor_FaultHandler(MotorFault_t fault)
{
    switch(fault) {
        case MOTOR_FAULT_OVERCURRENT:
            // 过流保护: 立即停止所有电机
            CarStop();
            break;

        case MOTOR_FAULT_STALL:
            // 堵转保护: 降低功率重试
            front_right_speed_duty *= 0.5;
            behind_left_speed_duty *= 0.5;
            break;

        case MOTOR_FAULT_PWM_ERROR:
            // PWM错误: 重置PWM系统
            speed_count = 0;
            break;

        default:
            break;
    }
}
```

### 8.2 保护机制
```c
//============================================================================
// 【电机保护机制】
//============================================================================

// 【软启动保护】
void Motor_SoftStart(int16_t target_speed)
{
    static int16_t current_speed = 0;
    static uint8_t startup_delay = 0;

    if(startup_delay < 10) {  // 10ms软启动时间
        startup_delay++;
        current_speed = target_speed * startup_delay / 10;
    } else {
        current_speed = target_speed;
    }

    front_right_speed_duty = current_speed;
    behind_left_speed_duty = current_speed;
}

// 【过载保护】
void Motor_OverloadProtection(void)
{
    static uint32_t overload_start_time = 0;

    if(motor_overload_detected) {
        if(overload_start_time == 0) {
            overload_start_time = HAL_GetTick();
        }

        // 过载超过5秒，强制停止
        if(HAL_GetTick() - overload_start_time > 5000) {
            CarStop();
            motor_protection_active = 1;
        }
    } else {
        overload_start_time = 0;
    }
}
```

## 9. 移植适配指南

### 9.1 硬件适配
```c
//============================================================================
// 【硬件平台适配】
//============================================================================

// 【GPIO引脚重映射】
#ifdef HARDWARE_V1
    #define MOTOR_GPIO_PORT    GPIOE
    #define FRONT_RIGHT_PIN1   GPIO_Pin_2
    #define FRONT_RIGHT_PIN2   GPIO_Pin_3
#endif

#ifdef HARDWARE_V2
    #define MOTOR_GPIO_PORT    GPIOD
    #define FRONT_RIGHT_PIN1   GPIO_Pin_0
    #define FRONT_RIGHT_PIN2   GPIO_Pin_1
#endif

// 【定时器资源适配】
#ifdef USE_TIM2
    #define MOTOR_TIMER        TIM2
    #define MOTOR_TIMER_IRQ    TIM2_IRQn
#endif

#ifdef USE_TIM3
    #define MOTOR_TIMER        TIM3
    #define MOTOR_TIMER_IRQ    TIM3_IRQn
#endif
```

### 9.2 移植检查清单
```c
//============================================================================
// 【电机控制移植检查清单】
//============================================================================

// 【硬件检查】
// - [ ] GPIO引脚可用性确认
// - [ ] 定时器资源可用性确认
// - [ ] 中断优先级配置
// - [ ] 电机驱动板兼容性
// - [ ] 供电电压匹配

// 【软件检查】
// - [ ] 定时器频率配置
// - [ ] PWM周期参数调整
// - [ ] 电机控制逻辑验证
// - [ ] 故障保护机制测试
// - [ ] 性能基准测试

// 【功能验证】
// - [ ] 基础运动功能测试
// - [ ] 差速控制精度测试
// - [ ] 响应时间测试
// - [ ] 长时间稳定性测试
// - [ ] 故障恢复测试
```

---
**文档状态**: 已完成
**交付物**: 完整的电机控制模块解析文档
**负责人**: Alex (工程师)
**下一步**: 生成模块移植指南
