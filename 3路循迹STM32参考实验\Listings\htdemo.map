Component: ARM Compiler 5.05 (build 41) Tool: armlink [4d0eb9]

==============================================================================

Section Cross References

    main.o(i.SearchRun) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.SearchRun) refers to main.o(.data) for .data
    main.o(i.main) refers to interface.o(i.delay_init) for delay_init
    main.o(i.main) refers to interface.o(i.GPIOCLKInit) for GPIOCLKInit
    main.o(i.main) refers to interface.o(i.UserLEDInit) for UserLEDInit
    main.o(i.main) refers to lcd1602.o(i.LCD1602Init) for LCD1602Init
    main.o(i.main) refers to interface.o(i.TIM2_Init) for TIM2_Init
    main.o(i.main) refers to motor.o(i.MotorInit) for MotorInit
    main.o(i.main) refers to interface.o(i.ServoInit) for ServoInit
    main.o(i.main) refers to interface.o(i.RedRayInit) for RedRayInit
    main.o(i.main) refers to interface.o(i.LEDToggle) for LEDToggle
    main.o(i.main) refers to main.o(i.SearchRun) for SearchRun
    main.o(i.main) refers to motor.o(i.CarGo) for CarGo
    main.o(i.main) refers to motor.o(i.CarBack) for CarBack
    main.o(i.main) refers to motor.o(i.CarLeft) for CarLeft
    main.o(i.main) refers to motor.o(i.CarRight) for CarRight
    main.o(i.main) refers to motor.o(i.CarStop) for CarStop
    main.o(i.main) refers to interface.o(i.Delayms) for Delayms
    main.o(i.main) refers to lcd1602.o(i.LCD1602WriteCommand) for LCD1602WriteCommand
    main.o(i.main) refers to main.o(.data) for .data
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    stm32f10x_it.o(i.EXTI15_10_IRQHandler) refers to irctrol.o(i.IRIntIsr) for IRIntIsr
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to motor.o(i.CarMove) for CarMove
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to main.o(.data) for tick_1ms
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to main.o(.data) for speed_count
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to main.o(.data) for tick_5ms
    stm32f10x_it.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    stm32f10x_it.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(i.USART3_IRQHandler) refers to main.o(.data) for bt_rec_flag
    stm32f10x_it.o(i.USART3_IRQHandler) refers to main.o(.data) for ctrl_comm
    stm32f10x_it.o(i.USART3_IRQHandler) refers to main.o(.data) for continue_time
    uart.o(i.PutChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    uart.o(i.PutChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    uart.o(i.PutNChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    uart.o(i.PutNChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    uart.o(i.PutStr) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    uart.o(i.PutStr) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    uart.o(i.USART3Conf) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    uart.o(i.USART3Conf) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    uart.o(i.USART3Conf) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    uart.o(i.USART3Conf) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    uart.o(i.USART3Conf) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    uart.o(i.USART3Conf) refers to stm32f10x_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    uart.o(i.USART3Conf) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    uart.o(i.USART3Conf) refers to misc.o(i.NVIC_Init) for NVIC_Init
    interface.o(i.Delayms) refers to interface.o(i.Delay_us) for Delay_us
    interface.o(i.GPIOCLKInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    interface.o(i.RedRayInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    interface.o(i.ServoInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    interface.o(i.ServoInit) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    interface.o(i.TIM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    interface.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    interface.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    interface.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    interface.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    interface.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    interface.o(i.UserLEDInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    interface.o(i.UserLEDInit) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    irctrol.o(i.DelayIr) refers to irctrol.o(i.DelayUs) for DelayUs
    irctrol.o(i.DelayUs) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    irctrol.o(i.DelayUs) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    irctrol.o(i.DelayUs) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    irctrol.o(i.IRCtrolInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    irctrol.o(i.IRCtrolInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    irctrol.o(i.IRCtrolInit) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    irctrol.o(i.IRCtrolInit) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    irctrol.o(i.IRCtrolInit) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    irctrol.o(i.IRCtrolInit) refers to misc.o(i.NVIC_Init) for NVIC_Init
    irctrol.o(i.IRCtrolInit) refers to irctrol.o(i.Time3Init) for Time3Init
    irctrol.o(i.IRIntIsr) refers to irctrol.o(i.DelayIr) for DelayIr
    irctrol.o(i.IRIntIsr) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    irctrol.o(i.IRIntIsr) refers to main.o(.data) for continue_time
    irctrol.o(i.IRIntIsr) refers to irctrol.o(.data) for .data
    irctrol.o(i.IRIntIsr) refers to main.o(.data) for ctrl_comm
    irctrol.o(i.Time3Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    irctrol.o(i.Time3Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    motor.o(i.CarBack) refers to main.o(.data) for front_left_speed_duty
    motor.o(i.CarBack) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarBack) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarBack) refers to main.o(.data) for behind_right_speed_duty
    motor.o(i.CarGo) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarGo) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarLeft) refers to main.o(.data) for front_left_speed_duty
    motor.o(i.CarLeft) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarLeft) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarLeft) refers to main.o(.data) for behind_right_speed_duty
    motor.o(i.CarMove) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.CarMove) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.CarMove) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarMove) refers to main.o(.data) for speed_count
    motor.o(i.CarMove) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarRight) refers to main.o(.data) for front_left_speed_duty
    motor.o(i.CarRight) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarRight) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarRight) refers to main.o(.data) for behind_right_speed_duty
    motor.o(i.CarStop) refers to main.o(.data) for front_left_speed_duty
    motor.o(i.CarStop) refers to main.o(.data) for front_right_speed_duty
    motor.o(i.CarStop) refers to main.o(.data) for behind_left_speed_duty
    motor.o(i.CarStop) refers to main.o(.data) for behind_right_speed_duty
    motor.o(i.MotorGPIO_Configuration) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.MotorInit) refers to motor.o(i.MotorGPIO_Configuration) for MotorGPIO_Configuration
    motor.o(i.MotorInit) refers to motor.o(i.CarStop) for CarStop
    lcd1602.o(i.LCD1602Init) refers to lcd1602.o(i.LCD1602PortInit) for LCD1602PortInit
    lcd1602.o(i.LCD1602Init) refers to interface.o(i.Delayms) for Delayms
    lcd1602.o(i.LCD1602Init) refers to lcd1602.o(i.LcdWriteCom) for LcdWriteCom
    lcd1602.o(i.LCD1602Init) refers to lcd1602.o(i.LcdWriteDate) for LcdWriteDate
    lcd1602.o(i.LCD1602Init) refers to lcd1602.o(.constdata) for .constdata
    lcd1602.o(i.LCD1602PortInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    lcd1602.o(i.LCD1602WriteCommand) refers to lcd1602.o(i.LcdWriteCom) for LcdWriteCom
    lcd1602.o(i.LCD1602WriteCommand) refers to lcd1602.o(i.LcdWriteDate) for LcdWriteDate
    lcd1602.o(i.LcdWriteCom) refers to interface.o(i.Delay_us) for Delay_us
    lcd1602.o(i.LcdWriteCom) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    lcd1602.o(i.LcdWriteCom) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lcd1602.o(i.LcdWriteDate) refers to interface.o(i.Delay_us) for Delay_us
    lcd1602.o(i.LcdWriteDate) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lcd1602.o(i.LcdWriteDate) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_can.o(i.CAN_Receive) refers to stm32f10x_can.o(i.CAN_FIFORelease) for CAN_FIFORelease
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing uart.o(i.PutChar), (32 bytes).
    Removing uart.o(i.PutNChar), (64 bytes).
    Removing uart.o(i.PutStr), (44 bytes).
    Removing uart.o(i.USART3Conf), (176 bytes).
    Removing irctrol.o(i.IRCtrolInit), (116 bytes).
    Removing irctrol.o(i.Time3Init), (44 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (68 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (72 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (36 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (8 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (136 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (204 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (14 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (142 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (232 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (126 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (164 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (28 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (20 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (224 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (34 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (42 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (196 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (96 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (102 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (44 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (36 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (148 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (150 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (22 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (84 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (46 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (48 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (172 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (28 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).

454 unused section(s) (total 14270 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    CMSIS\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    CMSIS\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    CMSIS\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    IRCtrol.c                                0x00000000   Number         0  irctrol.o ABSOLUTE
    LCD1602.c                                0x00000000   Number         0  lcd1602.o ABSOLUTE
    StdPeriph_Driver\src\misc.c              0x00000000   Number         0  misc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_adc.c     0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_bkp.c     0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_can.c     0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_cec.c     0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_crc.c     0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_dac.c     0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_dbgmcu.c  0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_dma.c     0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_exti.c    0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_flash.c   0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_fsmc.c    0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_gpio.c    0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_i2c.c     0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_iwdg.c    0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_pwr.c     0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_rcc.c     0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_rtc.c     0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_sdio.c    0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_spi.c     0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_tim.c     0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_usart.c   0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    StdPeriph_Driver\src\stm32f10x_wwdg.c    0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    interface.c                              0x00000000   Number         0  interface.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    startup_stm32f10x_hd.s                   0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800020c   Section        0  heapauxi.o(.text)
    .text                                    0x08000212   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800025c   Section        0  exit.o(.text)
    .text                                    0x08000268   Section        8  libspace.o(.text)
    .text                                    0x08000270   Section        0  sys_exit.o(.text)
    .text                                    0x0800027c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800027e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800027e   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.CarBack                                0x08000280   Section        0  motor.o(i.CarBack)
    i.CarGo                                  0x080002a8   Section        0  motor.o(i.CarGo)
    i.CarLeft                                0x080002bc   Section        0  motor.o(i.CarLeft)
    i.CarMove                                0x080002e8   Section        0  motor.o(i.CarMove)
    i.CarRight                               0x080003a8   Section        0  motor.o(i.CarRight)
    i.CarStop                                0x080003d4   Section        0  motor.o(i.CarStop)
    i.DebugMon_Handler                       0x080003f8   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.DelayIr                                0x080003fa   Section        0  irctrol.o(i.DelayIr)
    i.DelayUs                                0x08000410   Section        0  irctrol.o(i.DelayUs)
    i.Delay_us                               0x08000448   Section        0  interface.o(i.Delay_us)
    i.Delayms                                0x08000468   Section        0  interface.o(i.Delayms)
    i.EXTI15_10_IRQHandler                   0x0800047e   Section        0  stm32f10x_it.o(i.EXTI15_10_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x080004a0   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x080004ac   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.GPIOCLKInit                            0x080004cc   Section        0  interface.o(i.GPIOCLKInit)
    i.GPIO_Init                              0x0800050a   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x080005a6   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080005b4   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080005b8   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x080005bc   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.IRIntIsr                               0x080005c0   Section        0  irctrol.o(i.IRIntIsr)
    i.LCD1602Init                            0x080006c8   Section        0  lcd1602.o(i.LCD1602Init)
    i.LCD1602PortInit                        0x0800072c   Section        0  lcd1602.o(i.LCD1602PortInit)
    i.LCD1602WriteCommand                    0x08000798   Section        0  lcd1602.o(i.LCD1602WriteCommand)
    i.LEDToggle                              0x080007ac   Section        0  interface.o(i.LEDToggle)
    i.LcdWriteCom                            0x080007bc   Section        0  lcd1602.o(i.LcdWriteCom)
    i.LcdWriteDate                           0x08000820   Section        0  lcd1602.o(i.LcdWriteDate)
    i.MemManage_Handler                      0x08000884   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.MotorGPIO_Configuration                0x08000888   Section        0  motor.o(i.MotorGPIO_Configuration)
    i.MotorInit                              0x08000918   Section        0  motor.o(i.MotorInit)
    i.NMI_Handler                            0x08000926   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000928   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x0800098c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000990   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080009a8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RedRayInit                             0x080009c0   Section        0  interface.o(i.RedRayInit)
    i.SVC_Handler                            0x08000a44   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SearchRun                              0x08000a48   Section        0  main.o(i.SearchRun)
    i.ServoInit                              0x08000ab8   Section        0  interface.o(i.ServoInit)
    i.SetSysClockTo72                        0x08000ae8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000ae9   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000b90   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000b94   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08000bd4   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM2_Init                              0x08000c2c   Section        0  interface.o(i.TIM2_Init)
    i.TIM_ClearITPendingBit                  0x08000c8e   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08000c94   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetCounter                         0x08000ca8   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x08000cac   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08000cc4   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_SetCounter                         0x08000cd4   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_TimeBaseInit                       0x08000cd8   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART3_IRQHandler                      0x08000d74   Section        0  stm32f10x_it.o(i.USART3_IRQHandler)
    i.USART_ClearITPendingBit                0x08000db8   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_GetITStatus                      0x08000dc4   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ReceiveData                      0x08000e02   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08000e0a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.UserLEDInit                            0x08000e0c   Section        0  interface.o(i.UserLEDInit)
    i.delay_init                             0x08000e3c   Section        0  interface.o(i.delay_init)
    i.main                                   0x08000e4c   Section        0  main.o(i.main)
    .constdata                               0x08000ee8   Section       21  lcd1602.o(.constdata)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section        1  main.o(.data)
    .data                                    0x20000005   Section        1  main.o(.data)
    .data                                    0x20000006   Section        1  main.o(.data)
    .data                                    0x20000007   Section        1  main.o(.data)
    .data                                    0x20000008   Section        4  main.o(.data)
    .data                                    0x2000000c   Section        1  main.o(.data)
    .data                                    0x2000000d   Section        1  main.o(.data)
    .data                                    0x2000000e   Section        1  main.o(.data)
    .data                                    0x2000000f   Section        5  irctrol.o(.data)
    .bss                                     0x20000014   Section       96  libspace.o(.bss)
    HEAP                                     0x20000078   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000078   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000278   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000278   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000678   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001cd   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080001e9   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_two_region_memory                  0x0800020d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800020f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000211   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08000213   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800025d   Thumb Code    12  exit.o(.text)
    __user_libspace                          0x08000269   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000269   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000269   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000271   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800027d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800027d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800027f   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800027f   Thumb Code     0  indicate_semi.o(.text)
    CarBack                                  0x08000281   Thumb Code    22  motor.o(i.CarBack)
    CarGo                                    0x080002a9   Thumb Code    12  motor.o(i.CarGo)
    CarLeft                                  0x080002bd   Thumb Code    26  motor.o(i.CarLeft)
    CarMove                                  0x080002e9   Thumb Code   168  motor.o(i.CarMove)
    CarRight                                 0x080003a9   Thumb Code    26  motor.o(i.CarRight)
    CarStop                                  0x080003d5   Thumb Code    20  motor.o(i.CarStop)
    DebugMon_Handler                         0x080003f9   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    DelayIr                                  0x080003fb   Thumb Code    20  irctrol.o(i.DelayIr)
    DelayUs                                  0x08000411   Thumb Code    50  irctrol.o(i.DelayUs)
    Delay_us                                 0x08000449   Thumb Code    32  interface.o(i.Delay_us)
    Delayms                                  0x08000469   Thumb Code    22  interface.o(i.Delayms)
    EXTI15_10_IRQHandler                     0x0800047f   Thumb Code    32  stm32f10x_it.o(i.EXTI15_10_IRQHandler)
    EXTI_ClearITPendingBit                   0x080004a1   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x080004ad   Thumb Code    28  stm32f10x_exti.o(i.EXTI_GetITStatus)
    GPIOCLKInit                              0x080004cd   Thumb Code    62  interface.o(i.GPIOCLKInit)
    GPIO_Init                                0x0800050b   Thumb Code   156  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x080005a7   Thumb Code    14  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080005b5   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080005b9   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x080005bd   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    IRIntIsr                                 0x080005c1   Thumb Code   246  irctrol.o(i.IRIntIsr)
    LCD1602Init                              0x080006c9   Thumb Code    94  lcd1602.o(i.LCD1602Init)
    LCD1602PortInit                          0x0800072d   Thumb Code   100  lcd1602.o(i.LCD1602PortInit)
    LCD1602WriteCommand                      0x08000799   Thumb Code    20  lcd1602.o(i.LCD1602WriteCommand)
    LEDToggle                                0x080007ad   Thumb Code    10  interface.o(i.LEDToggle)
    LcdWriteCom                              0x080007bd   Thumb Code    90  lcd1602.o(i.LcdWriteCom)
    LcdWriteDate                             0x08000821   Thumb Code    90  lcd1602.o(i.LcdWriteDate)
    MemManage_Handler                        0x08000885   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    MotorGPIO_Configuration                  0x08000889   Thumb Code   130  motor.o(i.MotorGPIO_Configuration)
    MotorInit                                0x08000919   Thumb Code    14  motor.o(i.MotorInit)
    NMI_Handler                              0x08000927   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000929   Thumb Code    94  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x0800098d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000991   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080009a9   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RedRayInit                               0x080009c1   Thumb Code   122  interface.o(i.RedRayInit)
    SVC_Handler                              0x08000a45   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SearchRun                                0x08000a49   Thumb Code   102  main.o(i.SearchRun)
    ServoInit                                0x08000ab9   Thumb Code    42  interface.o(i.ServoInit)
    SysTick_Handler                          0x08000b91   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08000b95   Thumb Code    52  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08000bd5   Thumb Code    74  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM2_Init                                0x08000c2d   Thumb Code    98  interface.o(i.TIM2_Init)
    TIM_ClearITPendingBit                    0x08000c8f   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08000c95   Thumb Code    20  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetCounter                           0x08000ca9   Thumb Code     4  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x08000cad   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08000cc5   Thumb Code    16  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_SetCounter                           0x08000cd5   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_TimeBaseInit                         0x08000cd9   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART3_IRQHandler                        0x08000d75   Thumb Code    52  stm32f10x_it.o(i.USART3_IRQHandler)
    USART_ClearITPendingBit                  0x08000db9   Thumb Code    12  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_GetITStatus                        0x08000dc5   Thumb Code    62  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ReceiveData                        0x08000e03   Thumb Code     8  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08000e0b   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    UserLEDInit                              0x08000e0d   Thumb Code    42  interface.o(i.UserLEDInit)
    delay_init                               0x08000e3d   Thumb Code    14  interface.o(i.delay_init)
    main                                     0x08000e4d   Thumb Code   150  main.o(i.main)
    table1                                   0x08000ee8   Data          12  lcd1602.o(.constdata)
    table2                                   0x08000ef4   Data           9  lcd1602.o(.constdata)
    Region$$Table$$Base                      0x08000f00   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08000f20   Number         0  anon$$obj.o(Region$$Table)
    speed_count                              0x20000000   Data           4  main.o(.data)
    front_left_speed_duty                    0x20000004   Data           1  main.o(.data)
    front_right_speed_duty                   0x20000005   Data           1  main.o(.data)
    behind_left_speed_duty                   0x20000006   Data           1  main.o(.data)
    behind_right_speed_duty                  0x20000007   Data           1  main.o(.data)
    tick_5ms                                 0x20000008   Data           1  main.o(.data)
    tick_200ms                               0x20000009   Data           1  main.o(.data)
    ctrl_comm                                0x2000000a   Data           1  main.o(.data)
    ctrl_comm_last                           0x2000000b   Data           1  main.o(.data)
    tick_1ms                                 0x2000000c   Data           1  main.o(.data)
    continue_time                            0x2000000d   Data           1  main.o(.data)
    bt_rec_flag                              0x2000000e   Data           1  main.o(.data)
    ir_rec_flag                              0x2000000f   Data           1  irctrol.o(.data)
    IRCOM                                    0x20000010   Data           4  irctrol.o(.data)
    __libspace_start                         0x20000014   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000074   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00000f34, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00000f20, Max: 0x00080000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO         3422    RESET               startup_stm32f10x_hd.o
    0x08000130   0x00000008   Code   RO         3431  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO         3587    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO         3589    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO         3591    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000002   Code   RO         3458    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x00000000   Code   RO         3465    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3467    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3470    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3472    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3474    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3477    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3479    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3481    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3483    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3485    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3487    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3489    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3491    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3493    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3495    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3497    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3501    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3503    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3505    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO         3507    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x00000002   Code   RO         3508    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x00000002   Code   RO         3528    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x00000000   Code   RO         3542    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3545    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3548    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3550    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO         3553    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x00000002   Code   RO         3554    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001ac   0x00000000   Code   RO         3433    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x00000000   Code   RO         3435    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x00000006   Code   RO         3447    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x00000000   Code   RO         3437    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x00000004   Code   RO         3438    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x00000000   Code   RO         3440    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x00000008   Code   RO         3441    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x00000002   Code   RO         3462    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x00000000   Code   RO         3510    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x00000004   Code   RO         3511    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x00000006   Code   RO         3512    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x00000002   PAD
    0x080001cc   0x00000040   Code   RO         3423    .text               startup_stm32f10x_hd.o
    0x0800020c   0x00000006   Code   RO         3429    .text               c_w.l(heapauxi.o)
    0x08000212   0x0000004a   Code   RO         3449    .text               c_w.l(sys_stackheap_outer.o)
    0x0800025c   0x0000000c   Code   RO         3451    .text               c_w.l(exit.o)
    0x08000268   0x00000008   Code   RO         3459    .text               c_w.l(libspace.o)
    0x08000270   0x0000000c   Code   RO         3520    .text               c_w.l(sys_exit.o)
    0x0800027c   0x00000002   Code   RO         3531    .text               c_w.l(use_no_semi.o)
    0x0800027e   0x00000000   Code   RO         3533    .text               c_w.l(indicate_semi.o)
    0x0800027e   0x00000002   Code   RO           89    i.BusFault_Handler  stm32f10x_it.o
    0x08000280   0x00000028   Code   RO          314    i.CarBack           motor.o
    0x080002a8   0x00000014   Code   RO          315    i.CarGo             motor.o
    0x080002bc   0x0000002c   Code   RO          316    i.CarLeft           motor.o
    0x080002e8   0x000000c0   Code   RO          317    i.CarMove           motor.o
    0x080003a8   0x0000002c   Code   RO          318    i.CarRight          motor.o
    0x080003d4   0x00000024   Code   RO          319    i.CarStop           motor.o
    0x080003f8   0x00000002   Code   RO           90    i.DebugMon_Handler  stm32f10x_it.o
    0x080003fa   0x00000014   Code   RO          272    i.DelayIr           irctrol.o
    0x0800040e   0x00000002   PAD
    0x08000410   0x00000038   Code   RO          273    i.DelayUs           irctrol.o
    0x08000448   0x00000020   Code   RO          212    i.Delay_us          interface.o
    0x08000468   0x00000016   Code   RO          213    i.Delayms           interface.o
    0x0800047e   0x00000020   Code   RO           91    i.EXTI15_10_IRQHandler  stm32f10x_it.o
    0x0800049e   0x00000002   PAD
    0x080004a0   0x0000000c   Code   RO         1198    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x080004ac   0x00000020   Code   RO         1202    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x080004cc   0x0000003e   Code   RO          214    i.GPIOCLKInit       interface.o
    0x0800050a   0x0000009c   Code   RO         1552    i.GPIO_Init         stm32f10x_gpio.o
    0x080005a6   0x0000000e   Code   RO         1556    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080005b4   0x00000004   Code   RO         1559    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080005b8   0x00000004   Code   RO         1560    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080005bc   0x00000002   Code   RO           92    i.HardFault_Handler  stm32f10x_it.o
    0x080005be   0x00000002   PAD
    0x080005c0   0x00000108   Code   RO          275    i.IRIntIsr          irctrol.o
    0x080006c8   0x00000064   Code   RO          376    i.LCD1602Init       lcd1602.o
    0x0800072c   0x0000006c   Code   RO          377    i.LCD1602PortInit   lcd1602.o
    0x08000798   0x00000014   Code   RO          378    i.LCD1602WriteCommand  lcd1602.o
    0x080007ac   0x00000010   Code   RO          215    i.LEDToggle         interface.o
    0x080007bc   0x00000064   Code   RO          379    i.LcdWriteCom       lcd1602.o
    0x08000820   0x00000064   Code   RO          380    i.LcdWriteDate      lcd1602.o
    0x08000884   0x00000002   Code   RO           93    i.MemManage_Handler  stm32f10x_it.o
    0x08000886   0x00000002   PAD
    0x08000888   0x00000090   Code   RO          320    i.MotorGPIO_Configuration  motor.o
    0x08000918   0x0000000e   Code   RO          321    i.MotorInit         motor.o
    0x08000926   0x00000002   Code   RO           94    i.NMI_Handler       stm32f10x_it.o
    0x08000928   0x00000064   Code   RO          415    i.NVIC_Init         misc.o
    0x0800098c   0x00000002   Code   RO           95    i.PendSV_Handler    stm32f10x_it.o
    0x0800098e   0x00000002   PAD
    0x08000990   0x00000018   Code   RO         1971    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080009a8   0x00000018   Code   RO         1973    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080009c0   0x00000084   Code   RO          216    i.RedRayInit        interface.o
    0x08000a44   0x00000002   Code   RO           96    i.SVC_Handler       stm32f10x_it.o
    0x08000a46   0x00000002   PAD
    0x08000a48   0x00000070   Code   RO            1    i.SearchRun         main.o
    0x08000ab8   0x00000030   Code   RO          217    i.ServoInit         interface.o
    0x08000ae8   0x000000a8   Code   RO         3378    i.SetSysClockTo72   system_stm32f10x.o
    0x08000b90   0x00000002   Code   RO           97    i.SysTick_Handler   stm32f10x_it.o
    0x08000b92   0x00000002   PAD
    0x08000b94   0x00000040   Code   RO         3380    i.SystemInit        system_stm32f10x.o
    0x08000bd4   0x00000058   Code   RO           98    i.TIM2_IRQHandler   stm32f10x_it.o
    0x08000c2c   0x00000062   Code   RO          218    i.TIM2_Init         interface.o
    0x08000c8e   0x00000006   Code   RO         2606    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08000c94   0x00000014   Code   RO         2611    i.TIM_Cmd           stm32f10x_tim.o
    0x08000ca8   0x00000004   Code   RO         2630    i.TIM_GetCounter    stm32f10x_tim.o
    0x08000cac   0x00000018   Code   RO         2632    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08000cc4   0x00000010   Code   RO         2636    i.TIM_ITConfig      stm32f10x_tim.o
    0x08000cd4   0x00000004   Code   RO         2676    i.TIM_SetCounter    stm32f10x_tim.o
    0x08000cd8   0x0000009c   Code   RO         2682    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08000d74   0x00000044   Code   RO           99    i.USART3_IRQHandler  stm32f10x_it.o
    0x08000db8   0x0000000c   Code   RO         3131    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08000dc4   0x0000003e   Code   RO         3138    i.USART_GetITStatus  stm32f10x_usart.o
    0x08000e02   0x00000008   Code   RO         3148    i.USART_ReceiveData  stm32f10x_usart.o
    0x08000e0a   0x00000002   Code   RO          100    i.UsageFault_Handler  stm32f10x_it.o
    0x08000e0c   0x00000030   Code   RO          219    i.UserLEDInit       interface.o
    0x08000e3c   0x0000000e   Code   RO          220    i.delay_init        interface.o
    0x08000e4a   0x00000002   PAD
    0x08000e4c   0x0000009c   Code   RO            2    i.main              main.o
    0x08000ee8   0x00000015   Data   RO          381    .constdata          lcd1602.o
    0x08000efd   0x00000003   PAD
    0x08000f00   0x00000020   Data   RO         3585    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000678, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000004   Data   RW            3    .data               main.o
    0x20000004   0x00000001   Data   RW            4    .data               main.o
    0x20000005   0x00000001   Data   RW            5    .data               main.o
    0x20000006   0x00000001   Data   RW            6    .data               main.o
    0x20000007   0x00000001   Data   RW            7    .data               main.o
    0x20000008   0x00000004   Data   RW            8    .data               main.o
    0x2000000c   0x00000001   Data   RW            9    .data               main.o
    0x2000000d   0x00000001   Data   RW           10    .data               main.o
    0x2000000e   0x00000001   Data   RW           11    .data               main.o
    0x2000000f   0x00000005   Data   RW          277    .data               irctrol.o
    0x20000014   0x00000060   Zero   RW         3460    .bss                c_w.l(libspace.o)
    0x20000074   0x00000004   PAD
    0x20000078   0x00000200   Zero   RW         3421    HEAP                startup_stm32f10x_hd.o
    0x20000278   0x00000400   Zero   RW         3420    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       472         28          0          0          0       5311   interface.o
       340         24          0          5          0       2693   irctrol.o
       428         34         21          0          0       3723   lcd1602.o
       268         26          0         15          0     244517   main.o
       100          6          0          0          0       1108   misc.o
       534        116          0          0          0       4645   motor.o
        64         26        304          0       1536        828   startup_stm32f10x_hd.o
        44         10          0          0          0       1240   stm32f10x_exti.o
       178          0          0          0          0       3712   stm32f10x_gpio.o
       206         30          0          0          0       6211   stm32f10x_it.o
        48         12          0          0          0       1242   stm32f10x_rcc.o
       230         42          0          0          0       5899   stm32f10x_tim.o
        82          0          0          0          0       3514   stm32f10x_usart.o
       232         20          0          0          0       1693   system_stm32f10x.o

    ----------------------------------------------------------------------
      3242        <USER>        <GROUP>         20       1536     286368   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       270         <USER>          <GROUP>          0        100        576   Library Totals
         4          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       266         16          0          0         96        576   c_w.l

    ----------------------------------------------------------------------
       270         <USER>          <GROUP>          0        100        576   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3512        390        360         20       1636     283924   Grand Totals
      3512        390        360         20       1636     283924   ELF Image Totals
      3512        390        360         20          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3872 (   3.78kB)
    Total RW  Size (RW Data + ZI Data)              1656 (   1.62kB)
    Total ROM Size (Code + RO Data + RW Data)       3892 (   3.80kB)

==============================================================================

