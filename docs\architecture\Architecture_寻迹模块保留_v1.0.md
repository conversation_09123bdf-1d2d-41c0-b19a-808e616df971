# 架构设计文档 - 3路循迹STM32寻迹模块保留项目

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-31
- **负责人**: Bob (架构师)
- **项目名称**: 寻迹模块保留架构设计

## 2. 系统架构概述

### 2.1 当前系统架构分析
现有系统包含以下模块：
```
STM32F10x系统
├── 核心模块
│   ├── main.c (主控制逻辑)
│   ├── interface.c/h (硬件接口层)
│   └── stm32f10x_it.c/h (中断处理)
├── 功能模块
│   ├── motor.c/h (电机控制) ✅ 保留
│   ├── LCD1602.c/h (LCD显示) ❌ 移除
│   ├── LCD12864.c/h (LCD显示) ❌ 移除
│   ├── IRCtrol.c/h (红外遥控) ❌ 移除
│   └── uart.c/h (串口通信) ❌ 移除
└── 系统库
    ├── CMSIS (ARM核心库) ✅ 保留
    └── StdPeriph_Driver (STM32标准库) ✅ 保留
```

### 2.2 目标架构设计
简化后的系统架构：
```
STM32F10x寻迹系统
├── 应用层
│   └── main.c (寻迹控制逻辑)
├── 硬件抽象层
│   ├── interface.c/h (简化的硬件接口)
│   └── motor.c/h (电机控制)
├── 驱动层
│   └── stm32f10x_it.c/h (定时器中断)
└── 系统库
    ├── CMSIS
    └── StdPeriph_Driver
```

## 3. 模块依赖关系分析

### 3.1 寻迹功能核心依赖
**必需保留的组件**：
1. **寻迹传感器接口**
   - SEARCH_M_IO (中间传感器)
   - SEARCH_L_IO (左侧传感器)  
   - SEARCH_R_IO (右侧传感器)
   - GPIO配置: GPIOG Pin4, Pin6, Pin8

2. **电机控制接口**
   - 前左电机: GPIOG Pin13, Pin11
   - 前右电机: GPIOC Pin11, GPIOD Pin0
   - 后左电机: GPIOD Pin6, GPIOG Pin9
   - 后右电机: GPIOD Pin4, Pin2

3. **定时器系统**
   - TIM2: PWM控制和时序管理
   - 中断处理: TIM2_IRQHandler

4. **基础系统**
   - GPIO时钟使能
   - 延时函数
   - LED状态指示

### 3.2 可移除的组件
**非核心功能模块**：
1. **显示模块**
   - LCD1602相关GPIO (GPIOC Pin13-15, GPIOF Pin0-7)
   - LCD12864相关接口
   - 显示初始化和控制函数

2. **通信模块**
   - 红外遥控 (GPIOD Pin10)
   - 串口通信 (USART3)
   - 蓝牙控制变量

3. **传感器模块**
   - 超声波测距 (GPIOB Pin14, GPIOD Pin8)
   - 避障传感器 (GPIOC Pin7, GPIOG Pin2)
   - 速度传感器 (GPIOA Pin11-12)

4. **执行器模块**
   - 舵机控制 (GPIOD Pin12)

## 4. 数据流设计

### 4.1 寻迹数据流
```
传感器读取 → 寻迹算法 → 运动控制 → PWM输出
     ↓           ↓          ↓         ↓
SEARCH_*_IO → SearchRun() → Car*() → CarMove()
```

### 4.2 时序控制流
```
TIM2中断(100us) → tick计数 → 5ms周期 → 寻迹逻辑执行
                     ↓
                PWM占空比控制 → 电机速度调节
```

## 5. 接口设计

### 5.1 保留的接口定义
```c
// 寻迹传感器接口
#define SEARCH_M_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_8)
#define SEARCH_L_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_4)  
#define SEARCH_R_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_6)

// 电机控制接口
#define FRONT_LEFT_GO    FRONT_LEFT_F_SET; FRONT_LEFT_B_RESET
#define FRONT_RIGHT_GO   FRONT_RIGHT_F_SET; FRONT_RIGHT_B_RESET
#define BEHIND_LEFT_GO   BEHIND_LEFT_F_SET; BEHIND_LEFT_B_RESET
#define BEHIND_RIGHT_GO  BEHIND_RIGHT_F_SET; BEHIND_RIGHT_B_RESET

// 运动控制函数
void CarGo(void);    // 前进
void CarLeft(void);  // 左转
void CarRight(void); // 右转
void CarStop(void);  // 停止
```

### 5.2 移除的接口定义
```c
// 以下接口将被移除
- LCD相关宏定义 (LCDRS_*, LCDWR_*, LCDEN_*, LCD_PORT)
- 红外遥控宏定义 (IRIN_*)
- 超声波宏定义 (Echo_*, Trig_*)
- 舵机宏定义 (Servo_*)
- 避障传感器宏定义 (VOID_*)
- 速度传感器宏定义 (FRONT_*_S_*)
```

## 6. 内存和性能优化

### 6.1 内存优化
- 移除不必要的全局变量
- 简化中断处理逻辑
- 减少头文件包含

### 6.2 性能优化
- 保持5ms寻迹周期不变
- 优化GPIO读取效率
- 简化主循环逻辑

## 7. 风险评估

### 7.1 技术风险
- **风险**: 移除模块时可能影响GPIO配置
  - **缓解**: 仔细检查GPIO依赖关系
- **风险**: 定时器配置可能受影响
  - **缓解**: 保留TIM2完整配置

### 7.2 功能风险
- **风险**: 寻迹精度可能下降
  - **缓解**: 保持原有寻迹算法不变
- **风险**: 电机控制可能异常
  - **缓解**: 保留完整的电机控制逻辑

## 8. 实施建议

### 8.1 分步实施策略
1. **第一步**: 移除头文件引用
2. **第二步**: 移除初始化调用
3. **第三步**: 清理interface.h定义
4. **第四步**: 清理interface.c实现
5. **第五步**: 删除文件并验证

### 8.2 验证方案
- 每步完成后进行编译验证
- 保留原始代码备份
- 功能测试验证寻迹效果
