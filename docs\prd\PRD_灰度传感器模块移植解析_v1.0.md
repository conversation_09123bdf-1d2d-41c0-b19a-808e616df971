# STM32灰度传感器模块移植解析 - 产品需求文档

## 文档信息
- **文档标题**: 灰度传感器模块移植解析PRD
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **版本**: v1.0
- **项目**: 3路循迹STM32参考实验 - 模块移植

## 1. 背景与问题陈述

### 1.1 项目背景
老板需要将现有STM32三路循迹项目中的灰度传感器模块转接到其他工程中使用。当前项目包含了完整的循迹小车系统，包括三路和七路两套传感器系统，需要将其中的传感器相关模块进行解析和提取。

### 1.2 核心问题
- **模块耦合度高**: 传感器代码与电机控制、主控逻辑紧密耦合
- **缺乏模块化设计**: 各功能模块边界不清晰，难以独立提取
- **文档不完整**: 缺少详细的模块功能说明和移植指南
- **兼容性考虑**: 需要同时支持三路和七路两套传感器系统

### 1.3 解决目标
提供清晰、详细的灰度传感器模块解析，包括各个子模块的功能、接口、依赖关系，以及完整的移植指南。

## 2. 目标与成功指标

### 2.1 项目目标 (Objectives)
1. **模块清晰化**: 将灰度传感器相关功能按模块进行清晰分类和解析
2. **接口标准化**: 定义标准的模块接口，便于在其他项目中集成
3. **文档完整化**: 提供详细的技术文档和移植指南
4. **兼容性保证**: 确保移植后的模块能够在不同硬件平台上正常工作

### 2.2 关键结果 (Key Results)
- **模块解析完整度**: 100%覆盖所有传感器相关功能模块
- **接口文档完整性**: 每个模块都有详细的接口说明和使用示例
- **移植成功率**: 按照指南能够100%成功移植到目标工程
- **代码复用率**: 移植后的代码复用率达到90%以上

### 2.3 反向指标 (Counter Metrics)
- **模块耦合度**: 移植后模块间耦合度不超过20%
- **代码冗余度**: 重复代码不超过5%
- **集成复杂度**: 集成步骤不超过10个主要步骤

## 3. 用户画像与用户故事

### 3.1 目标用户
- **嵌入式开发工程师**: 需要在新项目中集成灰度传感器功能
- **STM32学习者**: 希望理解和学习灰度传感器的实现原理
- **项目集成者**: 需要将传感器模块集成到现有系统中

### 3.2 用户故事
- **作为**嵌入式开发工程师，**我希望**有清晰的模块划分，**以便**快速集成到我的项目中
- **作为**STM32学习者，**我希望**有详细的代码注释和说明，**以便**理解传感器工作原理
- **作为**项目集成者，**我希望**有标准化的接口，**以便**减少集成工作量

## 4. 功能规格详述

### 4.1 核心模块划分

#### 4.1.1 硬件抽象层模块
**模块名称**: 传感器硬件抽象层 (Sensor HAL)
**功能描述**: 
- GPIO引脚定义和配置
- 传感器初始化函数
- 底层数据读取接口
- 时钟配置管理

**关键接口**:
```c
// 传感器初始化
void SensorHAL_Init(SensorConfig_t* config);
// 读取原始数据
uint8_t SensorHAL_ReadRaw(uint8_t sensor_id);
// 批量读取
void SensorHAL_ReadAll(uint8_t* data_array);
```

#### 4.1.2 数据处理模块
**模块名称**: 传感器数据处理 (Sensor Data Processing)
**功能描述**:
- 原始数据滤波
- 信号去噪处理
- 数据格式转换
- 状态判断逻辑

**关键接口**:
```c
// 数据滤波
void SensorData_Filter(uint8_t* raw_data, uint8_t* filtered_data);
// 位置计算
int16_t SensorData_CalcPosition(uint8_t* sensor_data);
// 状态判断
TrackState_t SensorData_GetState(uint8_t* sensor_data);
```

#### 4.1.3 循迹算法模块
**模块名称**: 循迹控制算法 (Tracking Algorithm)
**功能描述**:
- 三路循迹算法
- 七路循迹算法
- PID控制器
- 状态机控制

**关键接口**:
```c
// 三路循迹
TrackCommand_t ThreeTrack_Run(uint8_t* sensor_data);
// 七路循迹
TrackCommand_t SevenTrack_Run(uint8_t* sensor_data);
// PID控制
int16_t PID_Calculate(PID_Controller_t* pid, int16_t error);
```

#### 4.1.4 配置管理模块
**模块名称**: 传感器配置管理 (Sensor Configuration)
**功能描述**:
- 传感器参数配置
- 算法参数调整
- 运行时配置修改
- 配置持久化

**关键接口**:
```c
// 配置初始化
void SensorConfig_Init(SensorConfig_t* config);
// 参数设置
void SensorConfig_SetParam(ConfigParam_t param, uint32_t value);
// 配置保存
void SensorConfig_Save(void);
```

### 4.2 数据结构设计

#### 4.2.1 传感器配置结构
```c
typedef struct {
    uint8_t sensor_count;           // 传感器数量 (3或7)
    GPIO_TypeDef* gpio_ports[7];    // GPIO端口数组
    uint16_t gpio_pins[7];          // GPIO引脚数组
    uint8_t filter_depth;           // 滤波深度
    uint16_t detection_threshold;   // 检测阈值
} SensorConfig_t;
```

#### 4.2.2 传感器数据结构
```c
typedef struct {
    uint8_t raw_data[7];           // 原始数据
    uint8_t filtered_data[7];      // 滤波数据
    int16_t line_position;         // 黑线位置
    TrackState_t track_state;      // 循迹状态
    uint32_t timestamp;            // 时间戳
} SensorData_t;
```

#### 4.2.3 PID控制器结构
```c
typedef struct {
    int16_t kp;                    // 比例系数
    int16_t ki;                    // 积分系数
    int16_t kd;                    // 微分系数
    int32_t integral;              // 积分累积
    int16_t last_error;            // 上次误差
    int16_t output_limit;          // 输出限制
} PID_Controller_t;
```

### 4.3 工作流程设计

#### 4.3.1 初始化流程
```
1. 系统时钟配置
2. GPIO时钟使能
3. 传感器GPIO初始化
4. 配置参数加载
5. 算法模块初始化
6. 自检测试
```

#### 4.3.2 运行时流程
```
1. 读取传感器原始数据
2. 数据滤波处理
3. 计算黑线位置
4. 判断循迹状态
5. 执行控制算法
6. 输出控制指令
```

## 5. 范围定义

### 5.1 包含功能 (In Scope)
- **三路传感器系统**: 完整的三路红外传感器实现
- **七路传感器系统**: 完整的七路传感器实现和PID控制
- **硬件抽象层**: GPIO配置、初始化、数据读取
- **数据处理算法**: 滤波、位置计算、状态判断
- **循迹控制算法**: 基础循迹逻辑和PID控制
- **配置管理**: 参数配置和运行时调整
- **移植指南**: 详细的移植步骤和注意事项

### 5.2 排除功能 (Out of Scope)
- **电机控制模块**: 不包含具体的电机驱动实现
- **通信模块**: 不包含串口、蓝牙等通信功能
- **显示模块**: 不包含LED、LCD等显示功能
- **存储模块**: 不包含EEPROM、Flash等存储功能
- **上位机软件**: 不包含PC端调试软件

## 6. 依赖与风险

### 6.1 内部依赖项
- **STM32标准库**: 依赖STM32F10x标准外设库
- **系统时钟**: 依赖系统时钟配置
- **GPIO资源**: 依赖特定的GPIO引脚资源
- **定时器资源**: 依赖定时器用于时序控制

### 6.2 外部依赖项
- **硬件平台**: 需要兼容的STM32硬件平台
- **编译环境**: 需要支持的开发环境和编译器
- **调试工具**: 需要相应的调试和下载工具

### 6.3 潜在风险
- **硬件兼容性风险**: 不同STM32型号的GPIO配置差异
- **时序依赖风险**: 对系统时钟和定时器的依赖
- **资源冲突风险**: GPIO引脚和其他模块的冲突
- **性能影响风险**: 算法复杂度对系统性能的影响

### 6.4 风险缓解策略
- **硬件抽象**: 通过HAL层屏蔽硬件差异
- **配置灵活性**: 提供灵活的配置选项
- **资源检查**: 提供资源冲突检查机制
- **性能优化**: 优化算法实现，减少计算开销

## 7. 发布初步计划

### 7.1 开发阶段
- **阶段1**: 模块解析和文档编写 (当前阶段)
- **阶段2**: 代码重构和模块化
- **阶段3**: 接口标准化和测试
- **阶段4**: 移植指南编写和验证

### 7.2 交付物清单
- **模块解析文档**: 详细的模块功能和接口说明
- **重构代码**: 模块化的传感器代码
- **移植指南**: 完整的移植步骤和示例
- **测试用例**: 功能验证和性能测试用例

### 7.3 验收标准
- **功能完整性**: 所有传感器功能正常工作
- **接口一致性**: 接口设计符合规范
- **文档完整性**: 文档覆盖所有功能点
- **移植成功率**: 按指南移植成功率100%

## 8. 任务分解与规划

### 8.1 主要任务清单

#### 8.1.1 传感器硬件模块解析
**任务描述**: 详细解析传感器硬件定义、GPIO配置、初始化流程等硬件相关模块
**交付物**:
- 硬件接口定义文档
- GPIO配置说明
- 初始化流程图
- 硬件兼容性分析

**关键要点**:
- 三路传感器硬件配置 (PG4, PG6, PG8)
- 七路传感器硬件配置 (PA0-PA3, PG4, PG6, PG8)
- GPIO时钟配置和使能
- 上拉输入模式配置原理

#### 8.1.2 循迹算法模块解析
**任务描述**: 解析三路循迹算法SearchRun和七路循迹算法的实现逻辑和控制策略
**交付物**:
- 三路循迹算法详解
- 七路循迹算法详解
- PID控制器实现
- 状态机设计文档

**关键要点**:
- SearchRun函数逻辑分析
- SevenTrack_Run函数实现
- PID参数调优方法
- 传感器状态组合处理

#### 8.1.3 电机控制模块解析
**任务描述**: 解析电机控制相关的PWM控制、运动函数等模块
**交付物**:
- 电机控制接口文档
- PWM生成机制说明
- 运动控制函数解析
- 差速控制原理

**关键要点**:
- CarGo/CarLeft/CarRight函数实现
- PWM占空比控制机制
- 电机速度调节方法
- 差速转向控制策略

#### 8.1.4 模块移植指南生成
**任务描述**: 基于模块解析结果，生成详细的移植指南和注意事项
**交付物**:
- 完整移植步骤指南
- 代码移植检查清单
- 常见问题解决方案
- 测试验证方法

**关键要点**:
- 依赖关系梳理
- 接口适配方法
- 配置参数调整
- 功能验证测试

### 8.2 执行时间线

| 任务阶段 | 负责人 | 预计时间 | 关键里程碑 |
|---------|--------|----------|------------|
| 硬件模块解析 | Bob | 15分钟 | 硬件接口文档完成 |
| 算法模块解析 | Alex | 20分钟 | 算法实现文档完成 |
| 电机控制解析 | Alex | 10分钟 | 控制接口文档完成 |
| 移植指南编写 | Emma | 15分钟 | 移植指南文档完成 |
| 整体验证测试 | David | 10分钟 | 验证报告完成 |

### 8.3 质量保证

#### 8.3.1 文档质量标准
- **完整性**: 覆盖所有相关功能模块
- **准确性**: 代码解析准确无误
- **可操作性**: 移植指南可直接执行
- **可维护性**: 文档结构清晰，便于更新

#### 8.3.2 代码质量标准
- **模块化**: 各模块职责清晰，耦合度低
- **可移植性**: 代码易于在不同平台移植
- **可配置性**: 支持灵活的参数配置
- **可测试性**: 提供完整的测试接口

### 8.4 验收标准

#### 8.4.1 功能验收
- [ ] 所有传感器模块功能正确解析
- [ ] 算法逻辑完整准确
- [ ] 接口定义清晰明确
- [ ] 移植步骤可执行

#### 8.4.2 文档验收
- [ ] 技术文档完整详细
- [ ] 代码注释清晰准确
- [ ] 移植指南操作性强
- [ ] 示例代码可运行

#### 8.4.3 质量验收
- [ ] 代码结构模块化
- [ ] 接口设计标准化
- [ ] 错误处理完善
- [ ] 性能优化合理

---
**文档状态**: 已完成
**下一步行动**: 进行详细的模块技术解析
**负责人**: Emma (产品经理)
**审核人**: Mike (团队领袖)
