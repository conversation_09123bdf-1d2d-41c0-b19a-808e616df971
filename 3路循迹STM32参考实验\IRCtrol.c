//============================================================================
// 【IRCtrol.c - 占位文件】
// 用途: 避免编译错误，红外遥控功能已移除
// 创建日期: 2025-01-31
// 说明: 仅保留寻迹功能，此模块为占位文件
//============================================================================

#include "stm32f10x.h"

//============================================================================
// 【占位函数实现】
//============================================================================

// 红外遥控初始化占位函数
void IR_Init(void)
{
    // 占位函数 - 无实际功能
    // 红外遥控功能已移除，仅保留寻迹功能
}

// 红外接收占位函数
uint8_t IR_Receive(void)
{
    // 占位函数 - 无实际功能
    return 0;
}

// 红外数据解码占位函数
uint32_t IR_Decode(void)
{
    // 占位函数 - 无实际功能
    return 0;
}

// 红外中断处理占位函数
void IR_IRQHandler(void)
{
    // 占位函数 - 无实际功能
}

//============================================================================
// 【兼容性函数】
//============================================================================

// 为了兼容可能的调用，提供空的实现
void IRIN_Configuration(void)
{
    // 兼容性函数 - 红外初始化配置
}

void IRIN_Init(void)
{
    // 兼容性函数
}

uint8_t IRIN_GetData(void)
{
    // 兼容性函数
    return 0;
}

// 红外遥控按键处理占位函数
void IR_KeyProcess(uint8_t key)
{
    // 占位函数 - 无实际功能
    (void)key; // 避免编译警告
}

// 红外遥控状态检查占位函数
uint8_t IR_CheckStatus(void)
{
    // 占位函数 - 无实际功能
    return 0;
}
