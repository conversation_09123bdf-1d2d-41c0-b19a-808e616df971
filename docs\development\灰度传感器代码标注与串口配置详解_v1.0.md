# STM32三路循迹项目 - 灰度传感器代码标注与串口配置详解

## 文档信息
- **文档标题**: 灰度传感器代码标注与串口配置详解
- **创建日期**: 2025-01-31
- **负责人**: Alex (工程师)
- **版本**: v1.0
- **项目**: 3路循迹STM32参考实验

## 1. 灰度传感器代码完整标注

### 1.1 传感器硬件定义 (interface.h)

```c
//============================================================================
// 【灰度传感器硬件定义区域】
//============================================================================

//循迹光电对管硬件连接定义
/* 
中循迹	SEARCH_M_PIN	PG8  - 中间传感器，检测正前方黑线
右循迹	SEARCH_R_PIN	PG6  - 右侧传感器，检测右偏黑线  
左循迹	SEARCH_L_PIN	PG4  - 左侧传感器，检测左偏黑线
 */

// 【中间传感器定义】
#define SEARCH_M_PIN         GPIO_Pin_8           // GPIO引脚号
#define SEARCH_M_GPIO        GPIOG                // GPIO端口组
#define SEARCH_M_IO          GPIO_ReadInputDataBit(SEARCH_M_GPIO, SEARCH_M_PIN)  // 实时读取宏

// 【右侧传感器定义】  
#define SEARCH_R_PIN         GPIO_Pin_6           // GPIO引脚号
#define SEARCH_R_GPIO        GPIOG                // GPIO端口组
#define SEARCH_R_IO          GPIO_ReadInputDataBit(SEARCH_R_GPIO, SEARCH_R_PIN)  // 实时读取宏

// 【左侧传感器定义】
#define SEARCH_L_PIN         GPIO_Pin_4           // GPIO引脚号
#define SEARCH_L_GPIO        GPIOG                // GPIO端口组  
#define SEARCH_L_IO          GPIO_ReadInputDataBit(SEARCH_L_GPIO, SEARCH_L_PIN)  // 实时读取宏

// 【传感器状态定义】
#define BLACK_AREA 1    // 检测到黑线(低反射率) - 传感器输出高电平
#define WHITE_AREA 0    // 检测到白色地面(高反射率) - 传感器输出低电平
```

### 1.2 传感器初始化代码 (interface.c)

```c
//============================================================================
// 【灰度传感器初始化函数】
//============================================================================

//红外光电对管初始化函数
void RedRayInit(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;  // GPIO配置结构体
	
	// 【中间传感器GPIO配置】
	GPIO_InitStructure.GPIO_Pin = SEARCH_M_PIN;        // 设置引脚: PG8
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;      // 上拉输入模式(确保悬空时为高电平)
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 设置GPIO响应速度
	GPIO_Init(SEARCH_M_GPIO , &GPIO_InitStructure);    // 应用配置到GPIOG
	
	// 【右侧传感器GPIO配置】
	GPIO_InitStructure.GPIO_Pin = SEARCH_R_PIN;        // 设置引脚: PG6
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;      // 上拉输入模式
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz响应速度
	GPIO_Init(SEARCH_R_GPIO , &GPIO_InitStructure);    // 应用配置到GPIOG
	
	// 【左侧传感器GPIO配置】
	GPIO_InitStructure.GPIO_Pin = SEARCH_L_PIN;        // 设置引脚: PG4
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;      // 上拉输入模式
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;  // 50MHz响应速度
	GPIO_Init(SEARCH_L_GPIO , &GPIO_InitStructure);    // 应用配置到GPIOG
}

//============================================================================
// 【GPIO时钟使能函数】
//============================================================================
void GPIOCLKInit(void)
{
	// 使能所有GPIO端口时钟(包括传感器使用的GPIOG)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOF , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOG , ENABLE);  // ← 传感器时钟使能
}
```

### 1.3 循迹算法核心代码 (main.c)

```c
//============================================================================
// 【循迹算法核心函数】
//============================================================================

//循迹控制函数 - 通过判断三个光电对管的状态来控制小车运动
void SearchRun(void)
{
	// 【场景1: 三路全检测】- 宽黑线或起始线
	if(SEARCH_M_IO == BLACK_AREA && SEARCH_L_IO == BLACK_AREA && SEARCH_R_IO == BLACK_AREA)
	{
		ctrl_comm = COMM_UP;    // 设置前进指令
		return;                 // 立即返回，优先级最高
	}

	// 【场景2: 右侧检测到黑线】- 黑线偏向右侧，需要右转修正
	if(SEARCH_R_IO == BLACK_AREA)
	{
		ctrl_comm = COMM_RIGHT; // 设置右转指令
	}
	// 【场景3: 左侧检测到黑线】- 黑线偏向左侧，需要左转修正  
	else if(SEARCH_L_IO == BLACK_AREA)
	{
		ctrl_comm = COMM_LEFT;  // 设置左转指令
	}
	// 【场景4: 仅中间检测到】- 理想状态，黑线居中
	else if(SEARCH_M_IO == BLACK_AREA)
	{
		ctrl_comm = COMM_UP;    // 设置前进指令
	}
	// 【场景5: 无检测】- 脱离黑线，保持上次动作(代码中未明确处理)
}

//============================================================================
// 【主循环中的传感器调用】
//============================================================================
int main(void)
{
	// 系统初始化
	delay_init();       // 延时函数初始化
	GPIOCLKInit();      // GPIO时钟使能 ← 包含传感器时钟
	UserLEDInit();      // LED初始化
	TIM2_Init();        // 定时器初始化
	MotorInit();        // 电机初始化
	RedRayInit();       // ← 【传感器初始化调用】

	while(1)
	{
		if(tick_5ms >= 5)   // 5ms周期检测
		{
			tick_5ms = 0;
			tick_200ms++;
			
			// LED状态更新(200ms周期)
			if(tick_200ms >= 40)
			{
				tick_200ms = 0;
				LEDToggle(LED_PIN);
			}
			
			// ← 【核心循迹算法调用】
			SearchRun();        // 执行传感器检测和决策
			
			// 指令变化检测和执行
			if(ctrl_comm_last != ctrl_comm)
			{
				ctrl_comm_last = ctrl_comm;
				switch(ctrl_comm)
				{
					case COMM_UP:    CarGo();break;     // 前进
					case COMM_DOWN:  CarBack();break;   // 后退
					case COMM_LEFT:  CarLeft();break;   // 左转
					case COMM_RIGHT: CarRight();break;  // 右转
					case COMM_STOP:  CarStop();break;   // 停止
					default : break;
				}
				Delayms(10);    // 10ms防抖延迟
			}
		}
	}
}
```

### 1.4 传感器状态组合分析表

| 左传感器(L) | 中传感器(M) | 右传感器(R) | 二进制状态 | 控制动作 | 应用场景 |
|-------------|-------------|-------------|------------|----------|----------|
| 0 | 0 | 0 | 000 | 保持上次 | 完全脱离黑线 |
| 0 | 0 | 1 | 001 | 右转 | 黑线偏向右侧 |
| 0 | 1 | 0 | 010 | 前进 | 理想循迹状态 |
| 0 | 1 | 1 | 011 | 右转 | 黑线偏右或弯道 |
| 1 | 0 | 0 | 100 | 左转 | 黑线偏向左侧 |
| 1 | 0 | 1 | 101 | 右转* | 交叉线或噪声 |
| 1 | 1 | 0 | 110 | 左转 | 黑线偏左或弯道 |
| 1 | 1 | 1 | 111 | 前进 | 宽黑线或起始线 |

*注: 101状态下右转优先级高于左转

## 2. 串口配置详细分析

### 2.1 串口硬件配置发现

通过代码分析发现，项目中包含USART3串口配置，主要用于蓝牙通信控制：

#### 2.1.1 串口中断处理代码 (stm32f10x_it.c)

```c
//============================================================================
// 【USART3串口中断处理函数】
//============================================================================

/**
  * @brief  USART3全局中断请求处理函数
  * @param  None  
  * @retval None
  * @note   用于接收蓝牙模块发送的控制指令
  */
void USART3_IRQHandler(void)
{
	unsigned char rec_data;  // 接收数据缓存
	
	// 检查接收中断标志位
	if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
	{
		// 清除中断标志位
		USART_ClearITPendingBit(USART3, USART_IT_RXNE);
		
		// 从接收数据寄存器读取一个字节
		rec_data = USART_ReceiveData(USART3);
		
		// 设置蓝牙接收标志位
		bt_rec_flag = 1;
		
		// 将接收到的数据作为控制指令
		ctrl_comm = rec_data;
		
		// 设置持续时间计数器
		continue_time = 40;
	}
}
```

#### 2.1.2 串口相关全局变量

```c
//============================================================================
// 【串口相关全局变量定义】
//============================================================================

extern unsigned char bt_rec_flag;    // 蓝牙接收标志位
extern unsigned char continue_time;  // 指令持续时间计数器
extern char ctrl_comm;               // 当前控制指令
```

### 2.2 串口配置参数推断

基于STM32F103标准配置和项目需求，推断串口配置参数：

#### 2.2.1 USART3基本配置

```c
//============================================================================
// 【推断的USART3配置参数】
//============================================================================

// 串口基本参数(基于STM32F103标准配置)
USART3配置参数:
- 波特率: 9600 bps (蓝牙模块常用波特率)
- 数据位: 8位
- 停止位: 1位  
- 校验位: 无校验
- 流控制: 无硬件流控制
- 模式: 接收模式使能

// GPIO配置(USART3标准引脚)
USART3_TX: PB10 (发送引脚) - 复用推挽输出
USART3_RX: PB11 (接收引脚) - 浮空输入或上拉输入
```

#### 2.2.2 完整串口初始化代码模板

```c
//============================================================================
// 【完整的USART3初始化函数模板】
//============================================================================

void USART3_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 1. 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);   // GPIO时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE); // USART3时钟
    
    // 2. GPIO配置
    // TX引脚配置 (PB10)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;         // 复用推挽输出
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    
    // RX引脚配置 (PB11)  
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;   // 浮空输入
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    
    // 3. USART配置
    USART_InitStructure.USART_BaudRate = 9600;                      // 波特率9600
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;     // 8位数据位
    USART_InitStructure.USART_StopBits = USART_StopBits_1;         // 1位停止位
    USART_InitStructure.USART_Parity = USART_Parity_No;            // 无校验
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; // 无流控
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; // 收发模式
    USART_Init(USART3, &USART_InitStructure);
    
    // 4. 中断配置
    NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;       // 抢占优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;              // 子优先级
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 5. 使能接收中断
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
    
    // 6. 使能USART3
    USART_Cmd(USART3, ENABLE);
}
```

### 2.3 串口连接方式详解

#### 2.3.1 硬件连接图

```
STM32F103ZE                    蓝牙模块(HC-05/HC-06)
┌─────────────┐               ┌─────────────┐
│             │               │             │
│    PB10 ────┼──────────────►│ RXD         │
│  (USART3_TX)│               │             │
│             │               │             │
│    PB11 ◄───┼───────────────│ TXD         │
│  (USART3_RX)│               │             │
│             │               │             │
│    VCC  ────┼──────────────►│ VCC (3.3V)  │
│             │               │             │
│    GND  ────┼──────────────►│ GND         │
│             │               │             │
└─────────────┘               └─────────────┘
```

#### 2.3.2 连接详细说明

| STM32引脚 | 蓝牙模块引脚 | 连接说明 |
|-----------|--------------|----------|
| PB10 (USART3_TX) | RXD | STM32发送数据到蓝牙模块 |
| PB11 (USART3_RX) | TXD | 蓝牙模块发送数据到STM32 |
| 3.3V | VCC | 电源正极(注意电压匹配) |
| GND | GND | 电源负极(共地) |

#### 2.3.3 蓝牙模块配置

```
蓝牙模块(HC-05)典型配置:
- 设备名称: HC-05
- 波特率: 9600 bps
- 数据位: 8位
- 停止位: 1位
- 校验位: 无
- 配对密码: 1234 或 0000
```

### 2.4 串口通信协议

#### 2.4.1 控制指令定义

```c
//============================================================================
// 【串口控制指令协议】
//============================================================================

// 运动控制指令(与循迹指令相同)
#define COMM_STOP  'I'    // 停止指令
#define COMM_UP    'A'    // 前进指令  
#define COMM_DOWN  'B'    // 后退指令
#define COMM_LEFT  'C'    // 左转指令
#define COMM_RIGHT 'D'    // 右转指令

// 指令处理流程:
// 1. 蓝牙接收到指令 → USART3_IRQHandler()
// 2. 设置bt_rec_flag = 1
// 3. 更新ctrl_comm = 接收到的指令
// 4. 主循环检测到指令变化 → 执行相应动作
// 5. continue_time计数器控制指令持续时间
```

#### 2.4.2 通信时序图

```
手机APP/上位机    蓝牙模块    STM32F103
      │              │           │
      │─────'A'─────►│           │
      │              │──'A'────►│ USART3_IRQHandler()
      │              │           │ bt_rec_flag = 1
      │              │           │ ctrl_comm = 'A'
      │              │           │
      │              │           │ 主循环检测到变化
      │              │           │ 执行CarGo()前进
      │              │           │
      │─────'I'─────►│           │
      │              │──'I'────►│ USART3_IRQHandler()
      │              │           │ ctrl_comm = 'I'
      │              │           │ 执行CarStop()停止
```

## 3. 项目中串口的实际状态

### 3.1 当前项目状态分析

通过代码分析发现：

1. **串口中断处理函数存在**: `USART3_IRQHandler()`已实现
2. **串口初始化函数缺失**: 未找到完整的USART3初始化代码
3. **串口相关变量未定义**: `bt_rec_flag`、`continue_time`等变量未在当前代码中定义
4. **串口库已包含**: `stm32f10x_usart.h`已在配置文件中包含

### 3.2 串口功能启用建议

要启用串口功能，需要添加以下代码：

#### 3.2.1 在interface.h中添加变量声明

```c
// 串口相关变量声明
extern unsigned char bt_rec_flag;     // 蓝牙接收标志位
extern unsigned char continue_time;   // 指令持续时间
```

#### 3.2.2 在main.c中添加变量定义

```c
// 串口相关全局变量
unsigned char bt_rec_flag = 0;        // 蓝牙接收标志位
unsigned char continue_time = 0;      // 指令持续时间计数器
```

#### 3.2.3 在interface.c中添加初始化函数

```c
// 添加完整的USART3初始化函数(参考2.2.2节)
void USART3_Init(void) { /* 完整代码见上文 */ }
```

#### 3.2.4 在main.c的main函数中调用

```c
int main(void)
{
    delay_init();
    GPIOCLKInit();
    UserLEDInit();
    TIM2_Init();
    MotorInit();
    RedRayInit();
    USART3_Init();        // ← 添加串口初始化
    
    while(1) { /* 主循环代码 */ }
}
```

## 4. 总结

### 4.1 灰度传感器代码特点

- **硬件配置简单**: 3个GPIO输入，上拉模式确保稳定性
- **读取方式直接**: 使用宏定义实现实时GPIO读取
- **算法逻辑清晰**: 基于状态机的简单判断逻辑
- **响应速度快**: 5ms检测周期，实时性良好

### 4.2 串口配置状态

- **部分实现**: 中断处理函数已实现，但初始化代码缺失
- **设计用途**: 主要用于蓝牙遥控功能
- **扩展潜力**: 可用于调试信息输出和参数配置

### 4.3 改进建议

1. **完善串口功能**: 添加完整的USART3初始化代码
2. **增加调试输出**: 利用串口输出传感器状态信息
3. **参数可配置**: 通过串口动态调整循迹参数
4. **错误处理**: 增加串口通信错误处理机制

---
**文档生成时间**: 2025-01-31  
**技术支持**: Alex (工程师)  
**适用对象**: 嵌入式开发工程师、STM32学习者
