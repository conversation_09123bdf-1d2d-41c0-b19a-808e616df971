//============================================================================
// 【LCD1602.c - 占位文件】
// 用途: 避免编译错误，LCD显示功能已移除
// 创建日期: 2025-01-31
// 说明: 仅保留寻迹功能，此模块为占位文件
//============================================================================

#include "stm32f10x.h"

//============================================================================
// 【占位函数实现】
//============================================================================

// LCD初始化占位函数
void LCD_Init(void)
{
    // 占位函数 - 无实际功能
    // LCD显示功能已移除，仅保留寻迹功能
}

// LCD显示字符串占位函数
void LCD_ShowString(uint8_t x, uint8_t y, char* str)
{
    // 占位函数 - 无实际功能
    (void)x;   // 避免编译警告
    (void)y;   // 避免编译警告
    (void)str; // 避免编译警告
}

// LCD清屏占位函数
void LCD_Clear(void)
{
    // 占位函数 - 无实际功能
}

// LCD显示字符占位函数
void LCD_ShowChar(uint8_t x, uint8_t y, char ch)
{
    // 占位函数 - 无实际功能
    (void)x;  // 避免编译警告
    (void)y;  // 避免编译警告
    (void)ch; // 避免编译警告
}

// LCD显示数字占位函数
void LCD_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len)
{
    // 占位函数 - 无实际功能
    (void)x;   // 避免编译警告
    (void)y;   // 避免编译警告
    (void)num; // 避免编译警告
    (void)len; // 避免编译警告
}

//============================================================================
// 【兼容性函数】
//============================================================================

// 为了兼容可能的调用，提供空的实现
void LCD1602_Init(void)
{
    // 兼容性函数
}

void LCD1602_Clear(void)
{
    // 兼容性函数
}

void LCD1602_ShowString(uint8_t line, uint8_t column, char* string)
{
    // 兼容性函数
    (void)line;   // 避免编译警告
    (void)column; // 避免编译警告
    (void)string; // 避免编译警告
}

void LCD1602_ShowNum(uint8_t line, uint8_t column, uint32_t number, uint8_t length)
{
    // 兼容性函数
    (void)line;   // 避免编译警告
    (void)column; // 避免编译警告
    (void)number; // 避免编译警告
    (void)length; // 避免编译警告
}

// LCD写命令占位函数
void LCD_WriteCommand(uint8_t command)
{
    // 占位函数 - 无实际功能
    (void)command; // 避免编译警告
}

// LCD写数据占位函数
void LCD_WriteData(uint8_t data)
{
    // 占位函数 - 无实际功能
    (void)data; // 避免编译警告
}
