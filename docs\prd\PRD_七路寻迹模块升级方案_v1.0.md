# 七路寻迹模块升级方案 - 产品需求文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **项目名称**: STM32三路寻迹升级为七路寻迹系统
- **目标平台**: STM32F103ZE

## 1. 背景与问题陈述

### 1.1 当前系统现状
现有系统采用**三路红外传感器**（左、中、右）进行黑线检测，虽然能够实现基本的循迹功能，但存在以下局限性：

- **精度不足**: 只能检测3个位置，对复杂路径适应性差
- **响应迟钝**: 转向判断较为粗糙，容易出现摆动
- **适应性差**: 无法处理急弯、交叉路口等复杂场景
- **稳定性不佳**: 在黑线宽度变化时容易失控

### 1.2 升级需求驱动
- **提高循迹精度**: 七路传感器提供更精细的位置检测
- **增强路径适应性**: 更好地处理复杂路径和急转弯
- **改善控制稳定性**: 减少摆动，提高循迹平滑度
- **扩展应用场景**: 支持更复杂的循迹竞赛和应用

## 2. 目标与成功指标

### 2.1 项目目标 (Objectives)
1. **功能升级**: 将三路传感器升级为七路传感器阵列
2. **算法优化**: 实现基于七路传感器的智能循迹算法
3. **性能提升**: 提高循迹精度和稳定性
4. **兼容性保持**: 保持现有硬件平台和接口兼容

### 2.2 关键结果 (Key Results)
- **传感器数量**: 从3路增加到7路
- **检测精度**: 位置检测精度提升至±1cm
- **响应速度**: 保持5ms检测周期不变
- **稳定性**: 减少50%的路径摆动
- **适应性**: 支持半径≥10cm的急转弯

### 2.3 反向指标 (Counter Metrics)
- **代码复杂度**: 不超过当前代码量的150%
- **内存占用**: 增加不超过100字节
- **GPIO占用**: 新增4个GPIO引脚
- **功耗**: 增加不超过20%

## 3. 用户画像与用户故事

### 3.1 目标用户
- **嵌入式学习者**: 希望学习更复杂的传感器阵列处理
- **竞赛参与者**: 需要高精度循迹系统参加机器人竞赛
- **技术研究者**: 研究多传感器融合和控制算法
- **产品开发者**: 开发商业化循迹机器人产品

### 3.2 用户故事
```
作为一个机器人竞赛参与者
我希望使用七路传感器系统
以便在复杂赛道上获得更好的成绩

作为一个嵌入式学习者  
我希望理解多传感器数据处理
以便掌握更高级的控制算法

作为一个产品开发者
我希望有稳定可靠的循迹系统
以便开发商业化的AGV产品
```

## 4. 功能规格详述

### 4.1 七路传感器硬件配置

#### 4.1.1 传感器布局设计
```
传感器阵列布局 (从左到右):
[S0] [S1] [S2] [S3] [S4] [S5] [S6]
 L3   L2   L1   M    R1   R2   R3

间距: 每个传感器间隔15mm
总宽度: 90mm
检测范围: 覆盖±45mm的黑线偏移
```

#### 4.1.2 GPIO引脚分配
| 传感器 | 位置 | GPIO端口 | GPIO引脚 | 原有占用 |
|--------|------|----------|----------|----------|
| S0 | 最左 | GPIOA | PA0 | 新增 |
| S1 | 左2 | GPIOA | PA1 | 新增 |
| S2 | 左1 | GPIOG | PG4 | 原左传感器 |
| S3 | 中间 | GPIOG | PG8 | 原中传感器 |
| S4 | 右1 | GPIOG | PG6 | 原右传感器 |
| S5 | 右2 | GPIOA | PA2 | 新增 |
| S6 | 最右 | GPIOA | PA3 | 新增 |

### 4.2 软件架构升级

#### 4.2.1 传感器数据结构
```c
// 七路传感器数据结构
typedef struct {
    uint8_t sensor_raw[7];      // 原始传感器数据
    uint8_t sensor_filtered[7]; // 滤波后数据
    int16_t line_position;      // 黑线位置 (-3000 到 +3000)
    uint8_t line_detected;      // 是否检测到黑线
    uint8_t sensor_count;       // 检测到的传感器数量
} SevenLineSensor_t;
```

#### 4.2.2 位置计算算法
```c
// 加权平均位置计算
int16_t CalculateLinePosition(uint8_t sensors[7]) {
    int32_t weighted_sum = 0;
    int32_t total_weight = 0;
    
    // 权重数组 (位置值)
    int16_t weights[7] = {-3000, -2000, -1000, 0, 1000, 2000, 3000};
    
    for(int i = 0; i < 7; i++) {
        if(sensors[i] == BLACK_AREA) {
            weighted_sum += weights[i];
            total_weight++;
        }
    }
    
    if(total_weight > 0) {
        return weighted_sum / total_weight;
    }
    return 0; // 未检测到黑线
}
```

### 4.3 控制算法升级

#### 4.3.1 PID控制器实现
```c
// PID控制器结构
typedef struct {
    float kp, ki, kd;           // PID参数
    int16_t last_error;         // 上次误差
    int32_t integral;           // 积分项
    int16_t output;             // 控制输出
} PID_Controller_t;

// PID控制计算
int16_t PID_Calculate(PID_Controller_t* pid, int16_t error) {
    // 比例项
    int32_t proportional = pid->kp * error;
    
    // 积分项
    pid->integral += error;
    int32_t integral = pid->ki * pid->integral;
    
    // 微分项  
    int32_t derivative = pid->kd * (error - pid->last_error);
    pid->last_error = error;
    
    // PID输出
    pid->output = proportional + integral + derivative;
    
    // 输出限幅
    if(pid->output > 1000) pid->output = 1000;
    if(pid->output < -1000) pid->output = -1000;
    
    return pid->output;
}
```

#### 4.3.2 自适应速度控制
```c
// 根据传感器状态调整速度
void AdaptiveSpeedControl(SevenLineSensor_t* sensor) {
    uint8_t active_sensors = sensor->sensor_count;
    
    if(active_sensors >= 5) {
        // 宽黑线或起始线 - 正常速度
        base_speed = NORMAL_SPEED;
    } else if(active_sensors <= 1) {
        // 可能脱线 - 降低速度
        base_speed = LOW_SPEED;
    } else if(abs(sensor->line_position) > 1500) {
        // 大偏移 - 降低速度
        base_speed = TURN_SPEED;
    } else {
        // 正常循迹 - 正常速度
        base_speed = NORMAL_SPEED;
    }
}
```

### 4.4 状态机控制逻辑

#### 4.4.1 循迹状态定义
```c
typedef enum {
    TRACK_NORMAL,       // 正常循迹
    TRACK_SHARP_LEFT,   // 急左转
    TRACK_SHARP_RIGHT,  // 急右转
    TRACK_LOST,         // 失去黑线
    TRACK_INTERSECTION, // 交叉路口
    TRACK_END           // 终点
} TrackState_t;
```

#### 4.4.2 状态判断逻辑
```c
TrackState_t DetermineTrackState(SevenLineSensor_t* sensor) {
    uint8_t pattern = GetSensorPattern(sensor->sensor_raw);
    
    switch(pattern) {
        case 0b0001000: // 仅中间
            return TRACK_NORMAL;
            
        case 0b1110000: // 左侧多个
            return TRACK_SHARP_LEFT;
            
        case 0b0000111: // 右侧多个  
            return TRACK_SHARP_RIGHT;
            
        case 0b1111111: // 全部检测
            return TRACK_INTERSECTION;
            
        case 0b0000000: // 无检测
            return TRACK_LOST;
            
        default:
            return TRACK_NORMAL;
    }
}
```

## 5. 范围定义

### 5.1 包含功能 (In Scope)
- ✅ 七路传感器硬件接口实现
- ✅ 传感器数据采集和滤波
- ✅ 加权平均位置计算算法
- ✅ PID控制器实现
- ✅ 自适应速度控制
- ✅ 状态机循迹逻辑
- ✅ 兼容现有电机控制接口
- ✅ 调试和测试功能

### 5.2 排除功能 (Out of Scope)
- ❌ 硬件电路板重新设计
- ❌ 传感器自动校准功能
- ❌ 机器学习算法集成
- ❌ 无线通信功能扩展
- ❌ 图形化调试界面
- ❌ 多机器人协同功能
- ❌ 路径规划和导航

## 6. 依赖与风险

### 6.1 内外部依赖
#### 内部依赖
- **现有代码库**: 基于当前三路系统进行扩展
- **GPIO资源**: 需要4个额外的GPIO引脚
- **定时器资源**: 复用现有TIM2定时器
- **内存资源**: 需要额外约100字节RAM

#### 外部依赖
- **传感器模块**: 需要采购4个额外的红外传感器
- **连接线缆**: 需要额外的杜邦线连接
- **测试环境**: 需要标准的黑线测试轨道

### 6.2 潜在风险
| 风险类型 | 风险描述 | 影响程度 | 缓解措施 |
|----------|----------|----------|----------|
| 技术风险 | GPIO资源不足 | 高 | 使用GPIOA的空闲引脚 |
| 性能风险 | 计算复杂度增加 | 中 | 优化算法，使用查表法 |
| 硬件风险 | 传感器干扰 | 中 | 增加滤波和屏蔽措施 |
| 集成风险 | 与现有代码冲突 | 低 | 渐进式集成和测试 |

## 7. 发布初步计划

### 7.1 开发阶段
#### 阶段1: 硬件接口开发 (2天)
- 新增传感器GPIO配置
- 扩展RedRayInit()函数
- 实现七路数据读取

#### 阶段2: 算法核心开发 (3天)  
- 位置计算算法实现
- PID控制器开发
- 状态机逻辑实现

#### 阶段3: 集成测试 (2天)
- 与现有系统集成
- 功能测试和调优
- 性能验证

### 7.2 测试计划
#### 单元测试
- 传感器数据读取测试
- 位置计算算法测试
- PID控制器测试

#### 集成测试
- 完整循迹功能测试
- 不同路径场景测试
- 稳定性和可靠性测试

#### 性能测试
- 响应时间测试
- 精度测试
- 功耗测试

### 7.3 部署方案
#### 灰度发布
1. **内部测试**: 开发团队内部验证
2. **小范围测试**: 选择简单路径测试
3. **全功能测试**: 复杂路径全面测试
4. **正式发布**: 完整功能发布

#### 数据跟踪
- 循迹精度指标监控
- 系统稳定性监控
- 用户反馈收集

## 8. 技术实现要点

### 8.1 关键技术挑战
1. **多传感器数据融合**: 如何有效处理7路传感器数据
2. **实时性保证**: 保持5ms检测周期的实时性要求
3. **算法优化**: 在有限资源下实现复杂控制算法
4. **兼容性维护**: 确保与现有系统的无缝集成

### 8.2 性能优化策略
1. **查表法**: 使用预计算表加速位置计算
2. **定点运算**: 避免浮点运算提高效率
3. **数据滤波**: 简单的移动平均滤波
4. **分级处理**: 根据场景选择不同的处理策略

### 8.3 可扩展性设计
1. **模块化架构**: 传感器、算法、控制分离
2. **参数配置**: 支持运行时参数调整
3. **接口标准化**: 便于后续功能扩展
4. **调试支持**: 内置调试和监控功能

---
**文档创建时间**: 2025-01-31  
**负责人**: Emma (产品经理)  
**审核状态**: 待技术评审  
**下一步**: 技术架构设计和开发计划制定
