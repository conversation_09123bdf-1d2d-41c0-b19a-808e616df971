# 七路循迹系统移植模块清单

## 文档信息
- **文档标题**: 七路循迹系统移植模块清单
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **版本**: v1.0
- **项目**: STM32七路循迹系统专项移植方案

## 1. 核心移植模块总览

### 1.1 七路循迹系统架构
```
七路循迹系统 (SevenTrack System)
├── 硬件抽象层
│   ├── 七路传感器GPIO配置
│   ├── 传感器数据读取接口
│   └── 硬件初始化函数
├── 数据处理层
│   ├── 原始数据采集模块
│   ├── 数字滤波处理模块
│   └── 位置计算算法模块
├── 控制算法层
│   ├── PID控制器模块
│   ├── 状态判断模块
│   └── 主控制循环模块
├── 电机控制层
│   ├── 差速控制算法
│   ├── 速度限制保护
│   └── 电机输出接口
└── 系统管理层
    ├── 模式切换控制
    ├── 参数配置管理
    └── 性能监控模块
```

## 2. 必需移植的核心模块

### 2.1 硬件抽象层模块

#### 模块1: 七路传感器硬件定义
```c
//============================================================================
// 【模块1: 七路传感器硬件定义】- 必需移植
//============================================================================

// 【传感器引脚定义】
#define SEARCH_S0_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0)  // 最左传感器
#define SEARCH_S1_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1)  // 左2传感器  
#define SEARCH_S2_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_4)  // 左1传感器
#define SEARCH_S3_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_8)  // 中间传感器
#define SEARCH_S4_IO  GPIO_ReadInputDataBit(GPIOG, GPIO_Pin_6)  // 右1传感器
#define SEARCH_S5_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_2)  // 右2传感器
#define SEARCH_S6_IO  GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_3)  // 最右传感器

// 【移植要点】
// 1. 根据目标硬件修改GPIO端口和引脚
// 2. 确保7个传感器引脚可用
// 3. 配置为上拉输入模式
// 4. 验证传感器信号逻辑(1=黑线, 0=白线)
```

#### 模块2: 七路传感器初始化
```c
//============================================================================
// 【模块2: 七路传感器初始化】- 必需移植
//============================================================================

void SevenRayInit(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 【时钟使能】
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOG, ENABLE);
    
    // 【GPIOA配置】(S0, S1, S5, S6)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;      // 上拉输入
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 【GPIOG配置】(S2, S3, S4)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_6 | GPIO_Pin_8;
    GPIO_Init(GPIOG, &GPIO_InitStructure);
}

// 【移植要点】
// 1. 根据实际硬件修改GPIO端口分配
// 2. 确保时钟正确使能
// 3. 验证上拉输入模式工作正常
```

### 2.2 数据处理层模块

#### 模块3: 传感器数据读取
```c
//============================================================================
// 【模块3: 传感器数据读取】- 核心功能模块
//============================================================================

void SevenSensor_ReadRaw(uint8_t data[7])
{
    data[0] = SEARCH_S0_IO;  // 最左传感器
    data[1] = SEARCH_S1_IO;  // 左2传感器
    data[2] = SEARCH_S2_IO;  // 左1传感器
    data[3] = SEARCH_S3_IO;  // 中间传感器
    data[4] = SEARCH_S4_IO;  // 右1传感器
    data[5] = SEARCH_S5_IO;  // 右2传感器
    data[6] = SEARCH_S6_IO;  // 最右传感器
}

// 【移植要点】
// 1. 确保传感器读取顺序正确(从左到右)
// 2. 验证数据类型匹配(uint8_t)
// 3. 测试读取速度满足实时要求(<1ms)
```

#### 模块4: 数字滤波处理
```c
//============================================================================
// 【模块4: 数字滤波处理】- 抗干扰模块
//============================================================================

void SevenSensor_Filter(uint8_t raw[7], uint8_t filtered[7])
{
    static uint8_t history[7][3] = {0};  // 3次历史数据
    static uint8_t index = 0;

    // 更新历史数据
    for(int i = 0; i < 7; i++) {
        history[i][index] = raw[i];

        // 计算3次平均值 - 多数表决滤波
        uint8_t sum = history[i][0] + history[i][1] + history[i][2];
        filtered[i] = (sum >= 2) ? 1 : 0;  // 多数表决
    }

    index = (index + 1) % 3;
}

// 【移植要点】
// 1. 静态变量确保历史数据保持
// 2. 多数表决算法有效抑制噪声
// 3. 滤波窗口可根据需要调整(3-5次)
```

#### 模块5: 位置计算算法
```c
//============================================================================
// 【模块5: 位置计算算法】- 核心算法模块
//============================================================================

int16_t SevenSensor_CalcPosition(uint8_t data[7])
{
    // 位置权重数组 (-3000 到 +3000)
    static const int16_t weights[7] = {-3000, -2000, -1000, 0, 1000, 2000, 3000};

    int32_t weighted_sum = 0;
    uint8_t active_count = 0;

    // 计算加权平均
    for(int i = 0; i < 7; i++) {
        if(data[i] == BLACK_AREA) {
            weighted_sum += weights[i];
            active_count++;
        }
    }

    // 返回位置值
    if(active_count > 0) {
        return (int16_t)(weighted_sum / active_count);
    }

    return 0;  // 未检测到黑线
}

// 【移植要点】
// 1. 权重数组定义了传感器位置映射
// 2. 加权平均算法计算黑线中心位置
// 3. 返回值范围: -3000(最左) ~ +3000(最右)
// 4. 0表示黑线在中心位置
```

### 2.3 控制算法层模块

#### 模块6: PID控制器
```c
//============================================================================
// 【模块6: PID控制器】- 核心控制模块
//============================================================================

// 【PID控制器结构】
typedef struct {
    int16_t kp;                     // 比例系数 (×1000)
    int16_t ki;                     // 积分系数 (×1000)
    int16_t kd;                     // 微分系数 (×1000)
    int32_t integral;               // 积分累积值
    int16_t last_error;             // 上次误差值
    int16_t output_limit;           // 输出限制值
    int16_t output;                 // 当前输出值
} PID_Controller_t;

// 【PID初始化】
void SevenTrack_PIDInit(PID_Controller_t* pid)
{
    pid->kp = 2000;         // 比例系数 = 2.0
    pid->ki = 0;            // 积分系数 = 0 (避免积分饱和)
    pid->kd = 1000;         // 微分系数 = 1.0
    pid->integral = 0;      // 积分累积清零
    pid->last_error = 0;    // 上次误差清零
    pid->output_limit = 2000; // 输出限制 ±2000
    pid->output = 0;        // 输出清零
}

// 【PID计算函数】
int16_t SevenTrack_PIDCalc(PID_Controller_t* pid, int16_t error)
{
    // 比例项
    int32_t proportional = (pid->kp * error) / 1000;

    // 积分项 (带限制)
    pid->integral += error;
    if(pid->integral > 10000) pid->integral = 10000;
    if(pid->integral < -10000) pid->integral = -10000;
    int32_t integral = (pid->ki * pid->integral) / 1000;

    // 微分项
    int32_t derivative = (pid->kd * (error - pid->last_error)) / 1000;
    pid->last_error = error;

    // PID输出
    int32_t output = proportional + integral + derivative;

    // 输出限制
    if(output > pid->output_limit) output = pid->output_limit;
    if(output < -pid->output_limit) output = -pid->output_limit;

    pid->output = (int16_t)output;
    return pid->output;
}

// 【移植要点】
// 1. PID参数需要根据实际系统调优
// 2. 定点运算避免浮点计算开销
// 3. 积分限幅防止积分饱和
// 4. 输出限幅保护电机系统
```

#### 模块7: 状态判断模块
```c
//============================================================================
// 【模块7: 状态判断模块】- 智能决策模块
//============================================================================

// 【循迹状态枚举】
typedef enum {
    TRACK_NORMAL = 0,       // 正常循迹
    TRACK_SHARP_LEFT,       // 急左转
    TRACK_SHARP_RIGHT,      // 急右转
    TRACK_LOST,             // 失去黑线
    TRACK_INTERSECTION      // 交叉路口
} TrackState_t;

TrackState_t SevenSensor_GetState(uint8_t data[7])
{
    uint8_t pattern = 0;
    uint8_t active_count = 0;

    // 生成传感器模式
    for(int i = 0; i < 7; i++) {
        if(data[i] == BLACK_AREA) {
            pattern |= (1 << i);
            active_count++;
        }
    }

    // 状态判断
    if(active_count == 0) {
        return TRACK_LOST;          // 无传感器检测到
    } else if(active_count >= 5) {
        return TRACK_INTERSECTION;  // 多传感器检测到
    } else if(pattern & 0x07) {     // 左侧传感器 (S0,S1,S2)
        return TRACK_SHARP_LEFT;
    } else if(pattern & 0x70) {     // 右侧传感器 (S4,S5,S6)
        return TRACK_SHARP_RIGHT;
    } else {
        return TRACK_NORMAL;        // 正常循迹
    }
}

// 【移植要点】
// 1. 位模式分析快速判断传感器组合
// 2. 状态机逻辑可根据实际需求调整
// 3. 阈值参数(5个传感器)可配置
```

### 2.4 电机控制层模块

#### 模块8: 差速控制算法
```c
//============================================================================
// 【模块8: 差速控制算法】- 运动控制模块
//============================================================================

void SevenTrack_MotorControl(int16_t correction)
{
    // 基础速度
    int16_t base_speed = SPEED_DUTY;  // 默认40 (80%功率)

    // 计算左右电机速度
    int16_t left_speed = base_speed - correction / 20;   // 左电机速度
    int16_t right_speed = base_speed + correction / 20;  // 右电机速度

    // 速度限制
    if(left_speed > 50) left_speed = 50;
    if(left_speed < -50) left_speed = -50;
    if(right_speed > 50) right_speed = 50;
    if(right_speed < -50) right_speed = -50;

    // 应用到电机
    front_left_speed_duty = left_speed;
    front_right_speed_duty = right_speed;
    behind_left_speed_duty = left_speed;
    behind_right_speed_duty = right_speed;
}

// 【移植要点】
// 1. correction除以20是速度调节系数，可调整
// 2. 速度限制保护电机和驱动器
// 3. 四轮同步控制确保运动一致性
```

### 2.5 系统管理层模块

#### 模块9: 主控制循环
```c
//============================================================================
// 【模块9: 主控制循环】- 系统核心模块
//============================================================================

// 【全局数据结构】
typedef struct {
    uint8_t sensor_raw[7];          // 原始传感器数据
    uint8_t sensor_filtered[7];     // 滤波后数据
    int16_t line_position;          // 黑线位置
    TrackState_t track_state;       // 循迹状态
} SevenLineSensor_t;

SevenLineSensor_t g_seven_sensor = {0};     // 七路传感器数据
PID_Controller_t g_line_pid = {0};          // 循迹PID控制器

void SevenTrack_Run(void)
{
    // 步骤1: 读取传感器数据
    SevenSensor_ReadRaw(g_seven_sensor.sensor_raw);

    // 步骤2: 数据滤波
    SevenSensor_Filter(g_seven_sensor.sensor_raw, g_seven_sensor.sensor_filtered);

    // 步骤3: 计算黑线位置
    g_seven_sensor.line_position = SevenSensor_CalcPosition(g_seven_sensor.sensor_filtered);

    // 步骤4: 判断循迹状态
    g_seven_sensor.track_state = SevenSensor_GetState(g_seven_sensor.sensor_filtered);

    // 步骤5: 计算位置误差 (目标位置为0)
    int16_t position_error = 0 - g_seven_sensor.line_position;

    // 步骤6: PID控制计算
    int16_t correction = SevenTrack_PIDCalc(&g_line_pid, position_error);

    // 步骤7: 根据状态调整控制策略
    switch(g_seven_sensor.track_state) {
        case TRACK_NORMAL:
            // 正常循迹 - 使用PID控制
            SevenTrack_MotorControl(correction);
            break;

        case TRACK_SHARP_LEFT:
            // 急左转 - 增强左转力度
            SevenTrack_MotorControl(correction * 2);
            break;

        case TRACK_SHARP_RIGHT:
            // 急右转 - 增强右转力度
            SevenTrack_MotorControl(correction * 2);
            break;

        case TRACK_LOST:
            // 失去黑线 - 保持上次动作
            break;

        case TRACK_INTERSECTION:
            // 交叉路口 - 直行
            SevenTrack_MotorControl(0);
            break;

        default:
            break;
    }
}

// 【移植要点】
// 1. 全局变量确保数据在函数间共享
// 2. 状态机控制提供智能决策
// 3. 执行周期建议5ms，确保实时性
```

#### 模块10: 模式切换控制
```c
//============================================================================
// 【模块10: 模式切换控制】- 系统管理模块
//============================================================================

// 【模式控制变量】
uint8_t g_seven_mode_enabled = 1;        // 1=七路模式, 0=三路模式

// 【智能循迹函数】
void SmartTrackRun(void)
{
    if(g_seven_mode_enabled) {
        // 使用七路寻迹算法
        SevenTrack_Run();
    } else {
        // 使用原三路寻迹算法
        SearchRun();
    }
}

// 【模式切换接口】
void SetTrackMode(uint8_t mode)
{
    g_seven_mode_enabled = mode;

    // 重新初始化对应的传感器系统
    if(mode) {
        SevenRayInit();                    // 初始化七路传感器
        SevenTrack_PIDInit(&g_line_pid);   // 初始化PID控制器
    } else {
        RedRayInit();                      // 初始化三路传感器
    }
}

// 【移植要点】
// 1. 支持运行时模式切换
// 2. 切换时需要重新初始化对应系统
// 3. 可通过外部接口或按键控制
```

## 3. 移植优先级和依赖关系

### 3.1 移植优先级分级
```
【高优先级 - 核心功能模块】(必须移植)
├── 模块1: 七路传感器硬件定义
├── 模块2: 七路传感器初始化
├── 模块3: 传感器数据读取
├── 模块5: 位置计算算法
├── 模块6: PID控制器
├── 模块8: 差速控制算法
└── 模块9: 主控制循环

【中优先级 - 增强功能模块】(建议移植)
├── 模块4: 数字滤波处理
├── 模块7: 状态判断模块
└── 模块10: 模式切换控制

【低优先级 - 可选功能模块】(可选移植)
├── 性能监控模块
├── 参数配置管理
└── 调试信息输出
```

### 3.2 模块依赖关系
```
依赖关系图:
模块1 (硬件定义) → 模块2 (初始化) → 模块3 (数据读取)
                                        ↓
模块4 (滤波处理) ← 模块3 (数据读取) → 模块5 (位置计算)
        ↓                              ↓
模块7 (状态判断) ← 模块5 (位置计算) → 模块6 (PID控制)
        ↓                              ↓
模块9 (主控制) ← 模块8 (差速控制) ← 模块6 (PID控制)
        ↓
模块10 (模式切换)
```

## 4. 分步移植指南

### 4.1 第一阶段: 基础硬件移植 (30分钟)
```
步骤1: 硬件定义移植
├── 1.1 复制传感器引脚定义 (模块1)
├── 1.2 修改GPIO端口和引脚号
├── 1.3 添加必要的头文件包含
└── 1.4 验证编译无错误

步骤2: 初始化函数移植 (模块2)
├── 2.1 复制SevenRayInit()函数
├── 2.2 修改GPIO配置参数
├── 2.3 确保时钟正确使能
└── 2.4 测试传感器读取功能

验证方法:
- 编译通过
- 传感器能正常读取数据
- GPIO配置正确
```

### 4.2 第二阶段: 数据处理移植 (45分钟)
```
步骤3: 数据读取移植 (模块3)
├── 3.1 复制SevenSensor_ReadRaw()函数
├── 3.2 验证传感器读取顺序
├── 3.3 测试数据读取速度
└── 3.4 确认数据格式正确

步骤4: 滤波算法移植 (模块4)
├── 4.1 复制SevenSensor_Filter()函数
├── 4.2 调整滤波参数
├── 4.3 测试滤波效果
└── 4.4 验证实时性能

步骤5: 位置计算移植 (模块5)
├── 5.1 复制SevenSensor_CalcPosition()函数
├── 5.2 调整权重数组参数
├── 5.3 测试位置计算精度
└── 5.4 验证边界条件

验证方法:
- 传感器数据读取稳定
- 滤波效果良好
- 位置计算准确
```

### 4.3 第三阶段: 控制算法移植 (60分钟)
```
步骤6: PID控制器移植 (模块6)
├── 6.1 复制PID相关结构体和函数
├── 6.2 调整PID参数
├── 6.3 测试PID响应特性
└── 6.4 优化控制性能

步骤7: 状态判断移植 (模块7)
├── 7.1 复制SevenSensor_GetState()函数
├── 7.2 调整状态判断阈值
├── 7.3 测试状态识别准确性
└── 7.4 验证状态切换逻辑

步骤8: 差速控制移植 (模块8)
├── 8.1 复制SevenTrack_MotorControl()函数
├── 8.2 调整速度控制参数
├── 8.3 测试电机响应
└── 8.4 验证差速效果

验证方法:
- PID控制稳定
- 状态判断准确
- 电机控制正常
```

### 4.4 第四阶段: 系统集成 (30分钟)
```
步骤9: 主控制循环移植 (模块9)
├── 9.1 复制SevenTrack_Run()函数
├── 9.2 集成所有子模块
├── 9.3 调整控制周期
└── 9.4 测试整体功能

步骤10: 模式切换移植 (模块10)
├── 10.1 复制SmartTrackRun()函数
├── 10.2 实现模式切换逻辑
├── 10.3 测试模式切换功能
└── 10.4 验证兼容性

验证方法:
- 七路循迹功能完整
- 模式切换正常
- 系统稳定运行
```

## 5. 关键配置参数

### 5.1 传感器配置参数
```c
//============================================================================
// 【传感器配置参数】
//============================================================================

// 【传感器数量】
#define SEVEN_SENSOR_COUNT    7

// 【传感器检测阈值】
#define BLACK_AREA           1        // 黑线检测值
#define WHITE_AREA           0        // 白线检测值

// 【滤波参数】
#define FILTER_WINDOW_SIZE   3        // 滤波窗口大小
#define FILTER_THRESHOLD     2        // 多数表决阈值

// 【位置权重】
static const int16_t position_weights[7] = {
    -3000, -2000, -1000, 0, 1000, 2000, 3000
};
```

### 5.2 PID控制参数
```c
//============================================================================
// 【PID控制参数】
//============================================================================

// 【默认PID参数】
#define DEFAULT_KP           2000     // 比例系数 (×1000)
#define DEFAULT_KI           0        // 积分系数 (×1000)
#define DEFAULT_KD           1000     // 微分系数 (×1000)

// 【PID限制参数】
#define PID_OUTPUT_LIMIT     2000     // 输出限制
#define PID_INTEGRAL_LIMIT   10000    // 积分限制

// 【控制周期】
#define CONTROL_PERIOD_MS    5        // 控制周期 5ms
```

### 5.3 电机控制参数
```c
//============================================================================
// 【电机控制参数】
//============================================================================

// 【速度参数】
#define BASE_SPEED           40       // 基础速度 (80%功率)
#define MAX_SPEED            50       // 最大速度
#define MIN_SPEED           -50       // 最小速度

// 【差速控制参数】
#define CORRECTION_RATIO     20       // 修正比例系数
#define SHARP_TURN_RATIO     2        // 急转弯增强比例

// 【状态控制参数】
#define INTERSECTION_SENSORS 5        // 交叉路口检测阈值
#define LEFT_SENSOR_MASK     0x07     // 左侧传感器掩码
#define RIGHT_SENSOR_MASK    0x70     // 右侧传感器掩码
```

## 6. 移植验证清单

### 6.1 功能验证清单
```
【基础功能验证】
├── [ ] 七路传感器数据读取正常
├── [ ] 数字滤波工作有效
├── [ ] 位置计算结果准确
├── [ ] PID控制响应正常
├── [ ] 状态判断逻辑正确
├── [ ] 差速控制效果良好
├── [ ] 主控制循环稳定
└── [ ] 模式切换功能正常

【性能验证清单】
├── [ ] 控制周期满足5ms要求
├── [ ] 传感器响应时间<1ms
├── [ ] PID计算时间<0.5ms
├── [ ] 整体循环时间<3ms
├── [ ] 内存占用<1KB
├── [ ] CPU占用<10%
└── [ ] 长时间运行稳定

【精度验证清单】
├── [ ] 位置计算精度±50
├── [ ] PID控制精度±2%
├── [ ] 差速控制精度±5%
├── [ ] 状态识别准确率>95%
└── [ ] 循迹偏差<2cm
```

### 6.2 兼容性验证清单
```
【硬件兼容性】
├── [ ] GPIO引脚配置正确
├── [ ] 时钟频率匹配
├── [ ] 中断优先级合理
├── [ ] 内存分配充足
└── [ ] 电源功耗可接受

【软件兼容性】
├── [ ] 编译器版本兼容
├── [ ] 库函数调用正确
├── [ ] 数据类型匹配
├── [ ] 函数接口一致
└── [ ] 全局变量无冲突

【系统兼容性】
├── [ ] 与三路模式兼容
├── [ ] 与电机控制兼容
├── [ ] 与其他模块兼容
├── [ ] 实时性要求满足
└── [ ] 稳定性要求达标
```

## 7. 故障排除指南

### 7.1 常见问题及解决方案
```
【传感器问题】
├── 传感器无响应
│   ├── 检查GPIO配置
│   ├── 检查引脚连接
│   └── 检查供电电压
├── 数据读取异常
│   ├── 检查读取顺序
│   ├── 检查数据类型
│   └── 检查时序要求
└── 滤波效果差
    ├── 调整滤波窗口
    ├── 修改阈值参数
    └── 检查噪声源

【控制问题】
├── PID控制不稳定
│   ├── 调整PID参数
│   ├── 检查积分限制
│   └── 优化微分项
├── 位置计算错误
│   ├── 检查权重数组
│   ├── 验证算法逻辑
│   └── 测试边界条件
└── 状态判断错误
    ├── 调整判断阈值
    ├── 检查位模式
    └── 验证状态机

【系统问题】
├── 性能不达标
│   ├── 优化算法效率
│   ├── 减少计算复杂度
│   └── 调整控制周期
├── 内存不足
│   ├── 优化数据结构
│   ├── 减少全局变量
│   └── 使用动态分配
└── 稳定性问题
    ├── 增加异常处理
    ├── 添加看门狗
    └── 优化中断处理
```

---
**文档状态**: 已完成
**交付物**: 完整的七路循迹系统移植模块清单
**负责人**: Emma (产品经理)
**总移植时间**: 约165分钟 (2.75小时)
**核心模块数量**: 10个必需模块
**移植复杂度**: 中等 (有详细指导)
