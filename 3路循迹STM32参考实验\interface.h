#ifndef __INTERFACE_H_
#define __INTERFACE_H_

#include "stm32f10x.h"

//user LED PG15
#define LED_PIN           GPIO_Pin_15
#define LED_GPIO        GPIOG
#define LED_SET         GPIO_SetBits(LED_GPIO , LED_PIN)
#define LED_RESET       GPIO_ResetBits(LED_GPIO , LED_PIN)

//电机控制IO定义 
/* 
FRONT_RIGHT_F_PIN	PC11	左前前进IO
FRONT_RIGHT_B_PIN	PD0	  左前后退IO

BEHIND_LEFT_F_PIN	PD6	    右后前进IO
BEHIND_LEFT_B_PIN	PG9	    右后后退IO

右后电机由于硬件问题，IO定义为低电平有效
BEHIND_RIGHT_F_PIN	PD4	    右后使能IO
BEHIND_RIGHT_B_PIN	PD2	    左前使能IO
 */
#define FRONT_LEFT_F_PIN         GPIO_Pin_13
#define FRONT_LEFT_F_GPIO        GPIOG
#define FRONT_LEFT_F_SET         GPIO_SetBits(FRONT_LEFT_F_GPIO , FRONT_LEFT_F_PIN)
#define FRONT_LEFT_F_RESET       GPIO_ResetBits(FRONT_LEFT_F_GPIO , FRONT_LEFT_F_PIN)

#define FRONT_LEFT_B_PIN         GPIO_Pin_11
#define FRONT_LEFT_B_GPIO        GPIOG
#define FRONT_LEFT_B_SET         GPIO_SetBits(FRONT_LEFT_B_GPIO , FRONT_LEFT_B_PIN)
#define FRONT_LEFT_B_RESET       GPIO_ResetBits(FRONT_LEFT_B_GPIO , FRONT_LEFT_B_PIN)

#define FRONT_RIGHT_F_PIN         GPIO_Pin_11
#define FRONT_RIGHT_F_GPIO        GPIOC
#define FRONT_RIGHT_F_SET         GPIO_SetBits(FRONT_RIGHT_F_GPIO , FRONT_RIGHT_F_PIN)
#define FRONT_RIGHT_F_RESET       GPIO_ResetBits(FRONT_RIGHT_F_GPIO , FRONT_RIGHT_F_PIN)

#define FRONT_RIGHT_B_PIN         GPIO_Pin_0
#define FRONT_RIGHT_B_GPIO        GPIOD
#define FRONT_RIGHT_B_SET         GPIO_SetBits(FRONT_RIGHT_B_GPIO , FRONT_RIGHT_B_PIN)
#define FRONT_RIGHT_B_RESET       GPIO_ResetBits(FRONT_RIGHT_B_GPIO , FRONT_RIGHT_B_PIN)

#define BEHIND_LEFT_F_PIN         GPIO_Pin_6
#define BEHIND_LEFT_F_GPIO        GPIOD
#define BEHIND_LEFT_F_SET         GPIO_SetBits(BEHIND_LEFT_F_GPIO , BEHIND_LEFT_F_PIN)
#define BEHIND_LEFT_F_RESET       GPIO_ResetBits(BEHIND_LEFT_F_GPIO , BEHIND_LEFT_F_PIN)

#define BEHIND_LEFT_B_PIN         GPIO_Pin_9
#define BEHIND_LEFT_B_GPIO        GPIOG
#define BEHIND_LEFT_B_SET         GPIO_SetBits(BEHIND_LEFT_B_GPIO , BEHIND_LEFT_B_PIN)
#define BEHIND_LEFT_B_RESET       GPIO_ResetBits(BEHIND_LEFT_B_GPIO , BEHIND_LEFT_B_PIN)

#define BEHIND_RIGHT_F_PIN         GPIO_Pin_4
#define BEHIND_RIGHT_F_GPIO        GPIOD
#define BEHIND_RIGHT_F_SET         GPIO_SetBits(BEHIND_RIGHT_F_GPIO , BEHIND_RIGHT_F_PIN)
#define BEHIND_RIGHT_F_RESET       GPIO_ResetBits(BEHIND_RIGHT_F_GPIO , BEHIND_RIGHT_F_PIN)

#define BEHIND_RIGHT_B_PIN         GPIO_Pin_2
#define BEHIND_RIGHT_B_GPIO        GPIOD
#define BEHIND_RIGHT_B_SET         GPIO_SetBits(BEHIND_RIGHT_B_GPIO , BEHIND_RIGHT_B_PIN)
#define BEHIND_RIGHT_B_RESET       GPIO_ResetBits(BEHIND_RIGHT_B_GPIO , BEHIND_RIGHT_B_PIN)

//============================================================================
// 【七路循迹传感器配置】- 升级版
//============================================================================
/*
七路传感器布局 (从左到右):
[S0] [S1] [S2] [S3] [S4] [S5] [S6]
 L3   L2   L1   M    R1   R2   R3

传感器间距: 15mm
总检测宽度: 90mm
GPIO分配: PA0,PA1,PG4,PG8,PG6,PA2,PA3
 */

// 【传感器数量定义】
#define SENSOR_COUNT 7

// 【S0 - 最左传感器 (L3)】
#define SEARCH_S0_PIN         GPIO_Pin_0
#define SEARCH_S0_GPIO        GPIOA
#define SEARCH_S0_IO          GPIO_ReadInputDataBit(SEARCH_S0_GPIO, SEARCH_S0_PIN)

// 【S1 - 左2传感器 (L2)】
#define SEARCH_S1_PIN         GPIO_Pin_1
#define SEARCH_S1_GPIO        GPIOA
#define SEARCH_S1_IO          GPIO_ReadInputDataBit(SEARCH_S1_GPIO, SEARCH_S1_PIN)

// 【S2 - 左1传感器 (L1) - 原左传感器】
#define SEARCH_S2_PIN         GPIO_Pin_4
#define SEARCH_S2_GPIO        GPIOG
#define SEARCH_S2_IO          GPIO_ReadInputDataBit(SEARCH_S2_GPIO, SEARCH_S2_PIN)

// 【S3 - 中间传感器 (M) - 原中传感器】
#define SEARCH_S3_PIN         GPIO_Pin_8
#define SEARCH_S3_GPIO        GPIOG
#define SEARCH_S3_IO          GPIO_ReadInputDataBit(SEARCH_S3_GPIO, SEARCH_S3_PIN)

// 【S4 - 右1传感器 (R1) - 原右传感器】
#define SEARCH_S4_PIN         GPIO_Pin_6
#define SEARCH_S4_GPIO        GPIOG
#define SEARCH_S4_IO          GPIO_ReadInputDataBit(SEARCH_S4_GPIO, SEARCH_S4_PIN)

// 【S5 - 右2传感器 (R2)】
#define SEARCH_S5_PIN         GPIO_Pin_2
#define SEARCH_S5_GPIO        GPIOA
#define SEARCH_S5_IO          GPIO_ReadInputDataBit(SEARCH_S5_GPIO, SEARCH_S5_PIN)

// 【S6 - 最右传感器 (R3)】
#define SEARCH_S6_PIN         GPIO_Pin_3
#define SEARCH_S6_GPIO        GPIOA
#define SEARCH_S6_IO          GPIO_ReadInputDataBit(SEARCH_S6_GPIO, SEARCH_S6_PIN)

// 【兼容性宏定义 - 保持原三路接口】
#define SEARCH_L_PIN         SEARCH_S2_PIN
#define SEARCH_L_GPIO        SEARCH_S2_GPIO
#define SEARCH_L_IO          SEARCH_S2_IO

#define SEARCH_M_PIN         SEARCH_S3_PIN
#define SEARCH_M_GPIO        SEARCH_S3_GPIO
#define SEARCH_M_IO          SEARCH_S3_IO

#define SEARCH_R_PIN         SEARCH_S4_PIN
#define SEARCH_R_GPIO        SEARCH_S4_GPIO
#define SEARCH_R_IO          SEARCH_S4_IO

// 【传感器状态定义】
#define BLACK_AREA 1    // 检测到黑线(低反射率)
#define WHITE_AREA 0    // 检测到白色地面(高反射率)

//左前
#define FRONT_LEFT_GO    FRONT_LEFT_F_SET; FRONT_LEFT_B_RESET//前进
#define FRONT_LEFT_BACK  FRONT_LEFT_F_RESET; FRONT_LEFT_B_SET//后退
#define FRONT_LEFT_STOP  FRONT_LEFT_F_RESET; FRONT_LEFT_B_RESET//停止

//右前
#define FRONT_RIGHT_GO     FRONT_RIGHT_F_SET;  FRONT_RIGHT_B_RESET
#define FRONT_RIGHT_BACK   FRONT_RIGHT_F_RESET;FRONT_RIGHT_B_SET
#define FRONT_RIGHT_STOP   FRONT_RIGHT_F_RESET;FRONT_RIGHT_B_RESET

//左后
#define BEHIND_LEFT_GO     BEHIND_LEFT_F_SET;BEHIND_LEFT_B_RESET
#define BEHIND_LEFT_BACK   BEHIND_LEFT_F_RESET;BEHIND_LEFT_B_SET
#define BEHIND_LEFT_STOP   BEHIND_LEFT_F_RESET;BEHIND_LEFT_B_RESET

//右后
#define BEHIND_RIGHT_GO    BEHIND_RIGHT_F_SET;BEHIND_RIGHT_B_RESET
#define BEHIND_RIGHT_BACK  BEHIND_RIGHT_F_RESET;BEHIND_RIGHT_B_SET
#define BEHIND_RIGHT_STOP  BEHIND_RIGHT_F_RESET;BEHIND_RIGHT_B_RESET
#define BEHIND_RIGHT_EN   BEHIND_RIGHT_F_SET;BEHIND_RIGHT_B_SET        
//小车由于左右两边各用一个驱动，所有左边两个电机只需要左后控制，右边电机只需右前控制，这里将右后电机控制脚当电机使能

#define SPEED_DUTY 40//默认占空比 按1ms最小分辨率 周期50ms计算

//指令定义
#define COMM_STOP  'I'//停止
#define COMM_UP    'A'//前进
#define COMM_DOWN  'B'//后退
#define COMM_LEFT  'C'//左转
#define COMM_RIGHT 'D'//右转

//============================================================================
// 【七路寻迹系统数据结构】
//============================================================================

// 七路传感器数据结构
typedef struct {
    uint8_t sensor_raw[7];          // 原始传感器数据
    uint8_t sensor_filtered[7];     // 滤波后数据
    int16_t line_position;          // 黑线位置 (-3000 到 +3000)
    uint8_t line_detected;          // 是否检测到黑线
    uint8_t sensor_count;           // 检测到的传感器数量
    uint8_t track_state;            // 循迹状态
} SevenLineSensor_t;

// PID控制器结构
typedef struct {
    int32_t kp, ki, kd;             // PID参数 (定点运算 * 1000)
    int16_t last_error;             // 上次误差
    int32_t integral;               // 积分项
    int16_t output;                 // 控制输出
    int16_t output_limit;           // 输出限制
} PID_Controller_t;

// 循迹状态枚举
typedef enum {
    TRACK_NORMAL = 0,       // 正常循迹
    TRACK_SHARP_LEFT,       // 急左转
    TRACK_SHARP_RIGHT,      // 急右转
    TRACK_LOST,             // 失去黑线
    TRACK_INTERSECTION,     // 交叉路口
    TRACK_END               // 终点
} TrackState_t;

//============================================================================
// 【全局变量声明】
//============================================================================
extern unsigned char tick_5ms;//5ms计数器，作为主函数的基本周期
extern unsigned char tick_1ms;//1ms计数器，作为电机的基本计数器
extern unsigned int speed_count;//占空比计数器 50次一周期

// 七路寻迹系统全局变量
extern SevenLineSensor_t g_seven_sensor;    // 七路传感器数据
extern PID_Controller_t g_line_pid;         // 循迹PID控制器
extern uint8_t g_seven_mode_enabled;        // 七路模式使能标志

//============================================================================
// 【函数声明】
//============================================================================

// 基础系统函数
void delay_init(void);
void Delayms(u32 Nms);
void Delay_us(u32 Nus);
void LEDToggle(uint16_t Led);

// 硬件初始化函数
void TIM2_Init(void);
void RedRayInit(void);              // 原三路传感器初始化
void SevenRayInit(void);            // 七路传感器初始化
void GPIOCLKInit(void);
void UserLEDInit(void);

// 七路寻迹核心函数
void SevenSensor_ReadRaw(uint8_t data[7]);                    // 读取原始数据
void SevenSensor_Filter(uint8_t raw[7], uint8_t filtered[7]); // 数据滤波
int16_t SevenSensor_CalcPosition(uint8_t data[7]);            // 位置计算
TrackState_t SevenSensor_GetState(uint8_t data[7]);           // 状态判断
void SevenTrack_PIDInit(PID_Controller_t* pid);              // PID初始化
int16_t SevenTrack_PIDCalc(PID_Controller_t* pid, int16_t error); // PID计算
void SevenTrack_Run(void);                                    // 七路循迹主函数
void SevenTrack_MotorControl(int16_t correction);             // 电机差速控制

#endif
