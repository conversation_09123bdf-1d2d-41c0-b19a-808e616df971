# PRD - 3路循迹STM32寻迹模块保留项目

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-31
- **负责人**: Emma (产品经理)
- **项目名称**: 仅保留寻迹模块，移除其他功能模块

## 2. 背景与问题陈述

### 2.1 当前状况
现有的3路循迹STM32参考实验代码包含了多个功能模块：
- **寻迹模块** (核心功能)
- 红外遥控模块 (IRCtrol)
- LCD显示模块 (LCD1602)
- 超声波测距模块
- 舵机控制模块
- 串口通信模块 (UART)
- 避障传感器模块

### 2.2 问题陈述
老板要求仅保留寻迹功能，移除所有其他模块，以简化代码结构，专注于核心的3路循迹功能。

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **O1**: 保留完整的3路寻迹功能
- **O2**: 移除所有非寻迹相关的模块和代码
- **O3**: 确保代码结构清晰，便于维护

### 3.2 关键结果 (Key Results)
- **KR1**: 寻迹功能正常工作，能够识别黑线并控制小车运动
- **KR2**: 代码行数减少至少50%
- **KR3**: 编译无错误，运行稳定

### 3.3 反向指标 (Counter Metrics)
- 寻迹精度不能降低
- 响应速度不能变慢

## 4. 用户画像与用户故事

### 4.1 目标用户
- 嵌入式开发学习者
- 需要简化寻迹代码的开发者

### 4.2 用户故事
- **作为**嵌入式学习者，**我希望**有一个纯净的寻迹代码，**以便**专注学习寻迹算法
- **作为**开发者，**我希望**代码结构简单，**以便**快速理解和修改

## 5. 功能规格详述

### 5.1 保留功能模块

#### 5.1.1 核心寻迹模块
- **3路红外传感器检测**: SEARCH_M_IO, SEARCH_L_IO, SEARCH_R_IO
- **寻迹算法**: SearchRun()函数
- **运动控制逻辑**: 前进、左转、右转

#### 5.1.2 电机控制模块
- **4轮电机控制**: 前左、前右、后左、后右
- **基本运动函数**: CarGo(), CarLeft(), CarRight(), CarStop()
- **PWM速度控制**: 保留速度占空比控制

#### 5.1.3 基础系统模块
- **GPIO初始化**: 寻迹传感器和电机控制IO
- **定时器模块**: TIM2用于PWM和时序控制
- **延时函数**: 基础延时功能
- **LED指示**: 保留状态指示LED

### 5.2 移除功能模块

#### 5.2.1 显示模块
- ❌ LCD1602显示模块 (LCD1602.c/h)
- ❌ LCD12864显示模块 (LCD12864.c/h)

#### 5.2.2 通信模块
- ❌ 红外遥控模块 (IRCtrol.c/h)
- ❌ 串口通信模块 (uart.c/h)

#### 5.2.3 传感器模块
- ❌ 超声波测距模块 (Echo/Trig相关)
- ❌ 避障传感器模块 (VOID_R/VOID_L相关)

#### 5.2.4 执行器模块
- ❌ 舵机控制模块 (Servo相关)

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 3路红外寻迹传感器读取
- 寻迹算法逻辑
- 4轮电机控制
- PWM速度控制
- 基础GPIO和定时器配置
- LED状态指示

### 6.2 排除功能 (Out of Scope)
- 所有显示功能
- 遥控功能
- 串口通信
- 超声波测距
- 避障功能
- 舵机控制

## 7. 依赖与风险

### 7.1 内部依赖
- STM32F10x标准库
- 基础GPIO和定时器驱动

### 7.2 潜在风险
- **风险1**: 移除模块时可能影响寻迹功能
  - **缓解措施**: 仔细分析依赖关系，逐步移除
- **风险2**: 编译错误
  - **缓解措施**: 分步骤移除，每步都进行编译验证

## 8. 发布初步计划

### 8.1 实施步骤
1. **阶段1**: 分析代码依赖关系
2. **阶段2**: 移除头文件引用
3. **阶段3**: 移除初始化调用
4. **阶段4**: 清理相关变量和函数
5. **阶段5**: 编译测试验证

### 8.2 验收标准
- 编译无错误
- 寻迹功能正常
- 代码结构清晰
